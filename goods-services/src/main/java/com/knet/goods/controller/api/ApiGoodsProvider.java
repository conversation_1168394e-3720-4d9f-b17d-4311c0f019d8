package com.knet.goods.controller.api;

import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.base.HttpResult;
import com.knet.goods.model.dto.req.CreateKnetProductRequest;
import com.knet.goods.model.dto.req.OffSaleKnetProductRequest;
import com.knet.goods.model.dto.req.QueryKnetProductRequest;
import com.knet.goods.model.dto.req.UpdatePriceKnetProductRequest;
import com.knet.goods.model.dto.resp.CreateKnetProductResp;
import com.knet.goods.model.dto.resp.OffSaleKnetProductResp;
import com.knet.goods.model.dto.resp.QueryKnetProductResp;
import com.knet.goods.service.IApiGoodsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/25 14:52
 * @description: 商品服务-对外提供接口
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = "商品服务-对外提供接口", description = "商品服务-对外提供接口")
public class ApiGoodsProvider {

    @Resource
    private IApiGoodsService apiGoodsService;

    /**
     * 创建上架商品
     *
     * @param request 创建商品请求体
     * @return 返回创建结果
     * @see com.knet.goods.model.dto.req.CreateKnetProductRequest
     */
    @ModifyHeader
    @Loggable(value = "knet 创建上架商品")
    @Operation(description = "创建上架商品")
    @PostMapping("/product/create")
    public HttpResult<CreateKnetProductResp> createProducts(@Validated @RequestBody CreateKnetProductRequest request) {
        return HttpResult.ok(apiGoodsService.createProducts(request));
    }

    /**
     * 商品下架
     *
     * @param request 商品下架请求体
     * @return 返回下架结果
     * @see com.knet.goods.model.dto.req.CreateKnetProductRequest
     */
    @ModifyHeader
    @Loggable(value = "knet 商品下架")
    @Operation(description = "商品下架")
    @PostMapping("/product/offSale")
    public HttpResult<OffSaleKnetProductResp> offSale(@Validated @RequestBody OffSaleKnetProductRequest request) {
        return HttpResult.ok(apiGoodsService.offSale(request));
    }

    /**
     * 更新商品价格
     *
     * @param request 更新商品价格请求体
     * @return 返回更新结果
     */
    @ModifyHeader
    @Loggable(value = "knet 更新商品价格")
    @Operation(description = "更新商品价格")
    @PostMapping("/product/updatePrice")
    public HttpResult<OffSaleKnetProductResp> updatePrice(@Validated @RequestBody UpdatePriceKnetProductRequest request) {
        return HttpResult.ok(apiGoodsService.updatePrice(request));
    }

    @ModifyHeader
    @Loggable(value = "knet 查询商品")
    @Operation(description = "外部查询商品")
    @PostMapping("/product/list")
    public HttpResult<List<QueryKnetProductResp>> queryKnetProducts(@Validated @RequestBody QueryKnetProductRequest request) {
        return HttpResult.ok(apiGoodsService.queryKnetProduct(request));
    }
}
