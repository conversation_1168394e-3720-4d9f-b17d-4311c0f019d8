package com.knet.oauth;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2025/2/12 15:10
 * @description: 授权服务 主启动类
 */
@ComponentScan(basePackages = {"com.knet.oauth", "com.knet.common"})
@MapperScan("com.knet.oauth.mapper")
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
public class OAuthServiceApp {
    public static void main(String[] args) {
        System.setProperty("spring.main.allow-circular-references", "true");
        SpringApplication.run(OAuthServiceApp.class);
        System.out.println(" 🚀 Oauth Service started successfully!");
    }

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
