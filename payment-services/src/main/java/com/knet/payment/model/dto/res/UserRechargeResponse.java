package com.knet.payment.model.dto.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/20 17:05
 * @description: 用户充值响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户充值响应")
public class UserRechargeResponse {
    
    @Schema(description = "充值记录ID", example = "REC-123456789012345678")
    private String recordId;
    
    @Schema(description = "用户ID", example = "1001")
    private Long userId;
    
    @Schema(description = "充值金额(美元)", example = "100.00")
    private String amount;
    
    @Schema(description = "充值后余额(美元)", example = "1100.00")
    private String balance;
    
    @Schema(description = "充值状态", example = "SUCCESS")
    private String status;
    
    @Schema(description = "充值时间", example = "2025-05-20 17:05:30")
    private String rechargeTime;
}
