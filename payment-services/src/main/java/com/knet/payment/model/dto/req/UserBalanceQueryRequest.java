package com.knet.payment.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 * @date 2025/5/20 16:30
 * @description: 用户余额查询请求
 */
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户余额查询请求")
public class UserBalanceQueryRequest extends BaseRequest {
    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long userId;
}
