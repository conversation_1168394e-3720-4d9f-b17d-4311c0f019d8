package com.knet.payment.model.dto.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/3 11:40
 * @description: 创建支付响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建支付响应")
public class CreatePaymentResponse {

    @Schema(description = "支付组ID", example = "GRP-123456789012345678")
    private String groupId;

    @Schema(description = "支付流水ID", example = "PAY-123456789012345678")
    private String paymentId;

    @Schema(description = "用户ID", example = "1001")
    private Long userId;

    @Schema(description = "关联订单ID", example = "ORD-123456789012345678")
    private String orderId;

    @Schema(description = "支付金额(美元)", example = "100.00")
    private String amount;

    @Schema(description = "支付渠道", example = "WALLET")
    private String paymentChannel;

    @Schema(description = "支付状态", example = "PENDING")
    private String status;

    @Schema(description = "渠道交易号（第三方支付返回）", example = "TXN-987654321")
    private String channelTxNo;

    @Schema(description = "支付创建时间", example = "2025-06-03 11:40:30")
    private String createTime;

    @Schema(description = "支付链接或二维码（部分渠道需要）", example = "https://pay.example.com/qr/123456")
    private String paymentUrl;

    @Schema(description = "错误信息（支付失败时）", example = "余额不足")
    private String errorMessage;

}
