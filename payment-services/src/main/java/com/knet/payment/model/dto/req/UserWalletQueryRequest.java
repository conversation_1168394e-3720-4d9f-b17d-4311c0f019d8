package com.knet.payment.model.dto.req;

import com.knet.common.base.BasePageRequest;
import com.knet.common.enums.WalletRecordType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/5/20 15:17
 * @description: UserAddressQueryRequest
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserWalletQueryRequest extends BasePageRequest {
    @Schema(description = "userId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "userId is mandatory")
    private Long userId;
    /**
     * @see WalletRecordType
     */
    @Schema(description = "交易类型,查询全部不传值")
    private WalletRecordType walletRecordType;
}
