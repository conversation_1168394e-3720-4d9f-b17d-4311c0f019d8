package com.knet.payment.model.dto.req;

import com.knet.common.base.BaseRequest;
import com.knet.common.enums.PaymentChannel;
import com.knet.common.exception.ServiceException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/3 11:35
 * @description: 创建支付请求
 */
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "创建支付请求")
public class CreatePaymentRequest extends BaseRequest {

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long userId;

    @NotBlank(message = "订单ID不能为空")
    @Schema(description = "关联订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORD-123456789012345678")
    private String orderId;

    @NotNull(message = "支付金额不能为空")
    @DecimalMin(value = "0.01", message = "支付金额必须大于0.01美元")
    @Schema(description = "支付金额(美元)", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
    private BigDecimal amount;

    @Builder.Default
    @Schema(description = "支付渠道", requiredMode = Schema.RequiredMode.REQUIRED, example = "WALLET,默认钱包余额支付")
    private PaymentChannel paymentChannel = PaymentChannel.WALLET;

    @Schema(description = "支付备注", example = "订单支付")
    private String remark;

    @Schema(description = "渠道特定参数（JSON格式）", example = "{\"cardNumber\":\"****1234\"}")
    private String channelParams;

    /**
     * 参数校验
     *
     * @param request 请求参数
     */
    public static void checkCreatePaymentRequest(CreatePaymentRequest request) {
        if (request.getAmount() == null || request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("支付金额必须大于0");
        }
        if (request.getPaymentChannel() == null) {
            throw new ServiceException("支付渠道不能为空");
        }
    }
}
