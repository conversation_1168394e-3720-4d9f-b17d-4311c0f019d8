package com.knet.payment.model.dto.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/20 16:32
 * @description: 用户余额响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户余额响应")
public class UserBalanceResponse {
    @Schema(description = "用户ID", example = "1001")
    private Long userId;
    @Schema(description = "可用余额(美元)", example = "100.00")
    private String balance;
    @Schema(description = "冻结金额(美元)", example = "10.00")
    private String frozenBalance;
    @Schema(description = "总余额(美元),展示总余额字段，其他忽略", example = "110.00")
    private String totalBalance;

    public UserBalanceResponse init(Long userId) {
        return UserBalanceResponse.builder()
                .userId(userId)
                .balance("0.00")
                .frozenBalance("0.00")
                .totalBalance("0.00")
                .build();
    }
}
