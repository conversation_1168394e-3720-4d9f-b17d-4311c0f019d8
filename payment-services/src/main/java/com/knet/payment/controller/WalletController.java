package com.knet.payment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.base.HttpResult;
import com.knet.payment.model.dto.req.UserBalanceQueryRequest;
import com.knet.payment.model.dto.req.UserRechargeRequest;
import com.knet.payment.model.dto.req.UserWalletQueryRequest;
import com.knet.payment.model.dto.res.SysWalletRecordResp;
import com.knet.payment.model.dto.res.UserBalanceResponse;
import com.knet.payment.model.dto.res.UserRechargeResponse;
import com.knet.payment.service.ISysUserWalletService;
import com.knet.payment.service.ISysWalletRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/20 15:14
 * @description: 支付服务-用户钱包控制器
 */
@Slf4j
@RestController
@RequestMapping("/wallet")
@Tag(name = "支付服务-用户钱包控制器", description = "支付服务-用户钱包控制器")
public class WalletController {
    @Resource
    private ISysWalletRecordService sysWalletRecordService;
    @Resource
    private ISysUserWalletService sysUserWalletService;

    /**
     * 查询用户钱包记录
     *
     * @param request 查询请求
     * @return 用户钱包记录列表
     */
    @Operation(summary = "查询用户钱包记录", description = "查询用户钱包记录")
    @PostMapping("/list")
    public HttpResult<IPage<SysWalletRecordResp>> list(@RequestBody UserWalletQueryRequest request) {
        return HttpResult.ok(sysWalletRecordService.findWalletRecordList(request));
    }

    @Operation(summary = "查询用户余额", description = "根据userId查询用户余额信息")
    @GetMapping("/balance/{userId}")
    public HttpResult<UserBalanceResponse> getUserBalance(@PathVariable(value = "userId", required = true) Long userId) {
        log.info("查询用户余额请求: userId={}", userId);
        UserBalanceResponse response = sysUserWalletService.getUserBalance(new UserBalanceQueryRequest(userId));
        return HttpResult.ok(response);
    }

    /**
     * 用户充值
     *
     * @param request 充值请求
     * @return 充值响应
     */
    @ModifyHeader("token")
    @Operation(description = "用户钱包充值，保存充值操作记录")
    @PostMapping("/recharge")
    public HttpResult<UserRechargeResponse> recharge(@Validated @RequestBody UserRechargeRequest request) {
        log.info("用户充值请求: {}", request);
        UserRechargeResponse response = sysUserWalletService.recharge(request);
        return HttpResult.ok(response);
    }
}
