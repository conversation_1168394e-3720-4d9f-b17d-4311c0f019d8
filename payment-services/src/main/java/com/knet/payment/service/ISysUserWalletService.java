package com.knet.payment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.payment.model.dto.req.UserBalanceQueryRequest;
import com.knet.payment.model.dto.req.UserRechargeRequest;
import com.knet.payment.model.dto.res.UserBalanceResponse;
import com.knet.payment.model.dto.res.UserRechargeResponse;
import com.knet.payment.model.entity.SysUserWallet;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 针对表【sys_user_wallet(用户钱包表)】的数据库操作Service
 * @date 2025-03-12 15:02:07
 */
public interface ISysUserWalletService extends IService<SysUserWallet> {

    /**
     * 查询用户余额
     *
     * @param request 查询请求
     * @return 用户余额信息
     */
    UserBalanceResponse getUserBalance(UserBalanceQueryRequest request);

    /**
     * 用户充值
     *
     * @param request 充值请求
     * @return 充值响应
     */
    UserRechargeResponse recharge(UserRechargeRequest request);

    /**
     * 扣减钱包余额
     *
     * @param userId 用户id
     * @param amount 扣减金额
     * @return 是否扣减成功
     */
    boolean deductionBalance(Long userId, BigDecimal amount);
}
