package com.knet.payment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.payment.model.entity.SysPaymentFlow;

/**
 * <AUTHOR>
 * @date 2025-03-12 13:37:21
 * @description: 针对表【sys_payment_flow(支付流水表)】的数据库操作Service
 */
public interface ISysPaymentFlowService extends IService<SysPaymentFlow> {

    /**
     * 支付成功
     *
     * @param paymentId 支付流水ID
     */
    void paySuccess(String paymentId);

    /**
     * 支付失败
     *
     * @param paymentId 支付流水ID
     */
    void payFailed(String paymentId);
}
