package com.knet.payment.service.impl;

import cn.hutool.core.date.DateUtil;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.enums.KnetPaymentFlowStatus;
import com.knet.common.enums.PaymentChannel;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.NumberUtils;
import com.knet.common.utils.RandomStrUtil;
import com.knet.payment.model.dto.req.CreatePaymentRequest;
import com.knet.payment.model.dto.res.CreatePaymentResponse;
import com.knet.payment.model.entity.SysPaymentFlow;
import com.knet.payment.model.entity.SysPaymentGroup;
import com.knet.payment.mq.producer.PaymentMessageProducer;
import com.knet.payment.service.*;
import com.knet.payment.strategy.PaymentStrategy;
import com.knet.payment.strategy.PaymentStrategyFactory;
import com.knet.payment.system.event.PaymentResultEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/3 11:50
 * @description: 支付服务实现类
 */
@Slf4j
@Service
public class PaymentServiceImpl implements IPaymentService {
    @Resource
    private RandomStrUtil randomStrUtil;
    @Resource
    private ISysPaymentGroupService paymentGroupService;
    @Resource
    private ISysPaymentFlowService paymentFlowService;
    @Resource
    private ISysUserWalletService userWalletService;
    @Resource
    private ISysWalletRecordService walletRecordService;
    @Resource
    private PaymentMessageProducer paymentMessageProducer;
    @Resource
    private ISysPaymentGroupService iSysPaymentGroupService;
    @Resource
    private PaymentStrategyFactory paymentStrategyFactory;

    /**
     * 创建支付
     *
     * @param request 创建支付请求
     * @return 创建支付响应
     */
    @DistributedLock(key = "'payment:create:'+#request.userId+':'+#request.orderId", expire = 5)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreatePaymentResponse createPayment(CreatePaymentRequest request) {
        CreatePaymentRequest.checkCreatePaymentRequest(request);
        Long userId = request.getUserId();
        String orderId = request.getOrderId();
        BigDecimal amount = request.getAmount();
        PaymentChannel paymentChannel = request.getPaymentChannel();
        log.info("创建支付开始: userId={}, orderId={}, amount={}, channel={}", userId, orderId, amount, paymentChannel.getName());
        try {
            // 1. 生成支付组ID和支付流水ID
            String groupId = randomStrUtil.getPaymentGroupId();
            String paymentId = randomStrUtil.getPaymentId();
            //2. 创建支付组
            SysPaymentGroup paymentGroup = SysPaymentGroup.create(groupId, userId, orderId, amount);
            boolean groupSaved = paymentGroupService.save(paymentGroup);
            if (!groupSaved) {
                throw new ServiceException("支付组创建失败");
            }
            // 3. 创建支付流水
            SysPaymentFlow paymentFlow = SysPaymentFlow.create(paymentId, groupId, paymentChannel, amount);
            boolean flowSaved = paymentFlowService.save(paymentFlow);
            if (!flowSaved) {
                throw new ServiceException("支付流水创建失败");
            }
            // 4. 根据支付渠道处理支付
            CreatePaymentResponse response = processPaymentByChannel(paymentFlow, request);
            log.info("创建支付成功: paymentId={}, groupId={}, status={}", paymentId, groupId, response.getStatus());
            return response;
        } catch (Exception e) {
            log.error("创建支付失败: userId={}, orderId={}, error={}", userId, orderId, e.getMessage(), e);
            throw new ServiceException("创建支付失败: " + e.getMessage());
        }
    }

    /**
     * 根据支付渠道处理支付（使用策略模式）
     */
    private CreatePaymentResponse processPaymentByChannel(SysPaymentFlow paymentFlow, CreatePaymentRequest request) {
        PaymentChannel channel = PaymentChannel.fromCode(paymentFlow.getPayChannel());
        String currentTime = DateUtil.now();
        log.info("开始处理支付: paymentId={}, channel={}", paymentFlow.getPaymentId(), channel.getName());
        // 构建响应构建器
        CreatePaymentResponse.CreatePaymentResponseBuilder responseBuilder = CreatePaymentResponse
                .builder()
                .groupId(paymentFlow.getGroupId())
                .paymentId(paymentFlow.getPaymentId())
                .userId(request.getUserId())
                .orderId(request.getOrderId())
                .amount(NumberUtils.formatDecimal(paymentFlow.getAmount()))
                .paymentChannel(channel.getName())
                .createTime(currentTime);
        try {
            // 处理支付
            PaymentStrategy strategy = paymentStrategyFactory.getStrategy(channel);
            CreatePaymentResponse response = strategy.processPayment(paymentFlow, request, responseBuilder);
            // 发送支付结果事件
            sendPaymentResultEvent(paymentFlow, request, response.getStatus(), response.getChannelTxNo());
            log.info("支付处理完成: paymentId={}, status={}", paymentFlow.getPaymentId(), response.getStatus());
            return response;
        } catch (Exception e) {
            log.error("支付处理失败: paymentId={}, channel={}, error={}",
                    paymentFlow.getPaymentId(), channel.getName(), e.getMessage());
            // 支付失败，发送MQ消息
            sendPaymentFailMessage(paymentFlow, request, e.getMessage());
            throw e;
        }
    }


    /**
     * 发送支付失败消息
     */
    private void sendPaymentFailMessage(SysPaymentFlow paymentFlow, CreatePaymentRequest request, String errorMessage) {
        try {
            // 更新支付流水状态为失败
            paymentFlow.setStatus(KnetPaymentFlowStatus.FAILED);
            paymentFlowService.updateById(paymentFlow);
            PaymentChannel channel = PaymentChannel.fromCode(paymentFlow.getPayChannel());
            // 发送支付失败事件 (payment.failed)
            paymentMessageProducer.sendPaymentFailedEvent(
                    paymentFlow.getPaymentId(),
                    request.getUserId(),
                    request.getOrderId(),
                    NumberUtils.formatDecimal(paymentFlow.getAmount()),
                    channel.getName(),
                    errorMessage
            );
        } catch (Exception e) {
            log.error("发送支付失败消息异常: paymentId={}, error={}",
                    paymentFlow.getPaymentId(), e.getMessage());
        }
    }

    /**
     * 发送支付结果事件
     */
    private void sendPaymentResultEvent(SysPaymentFlow paymentFlow, CreatePaymentRequest request, String status, String channelTxNo) {
        try {
            PaymentResultEvent resultEvent = PaymentResultEvent.create(paymentFlow, request, status, channelTxNo);
            paymentMessageProducer.sendPaymentResultEvent(resultEvent);
        } catch (Exception e) {
            log.error("发送支付结果事件异常: paymentId={}, error={}", paymentFlow.getPaymentId(), e.getMessage());
        }
    }

    @Override
    public CreatePaymentResponse queryPaymentStatus(String paymentId) {
        // TODO: 实现查询支付状态
        throw new ServiceException("查询支付状态功能待实现");
    }

    @Override
    public boolean cancelPayment(String paymentId) {
        // TODO: 实现取消支付
        throw new ServiceException("取消支付功能待实现");
    }
}
