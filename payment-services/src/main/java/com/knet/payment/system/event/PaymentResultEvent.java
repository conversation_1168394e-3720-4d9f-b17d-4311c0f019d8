package com.knet.payment.system.event;

import com.knet.common.enums.PaymentChannel;
import com.knet.common.utils.NumberUtils;
import com.knet.payment.model.dto.req.CreatePaymentRequest;
import com.knet.payment.model.entity.SysPaymentFlow;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/3 13:26
 * @description: 支付结果事件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentResultEvent implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "支付流水ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "PAY-123456789012345678")
    private String paymentId;
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long userId;
    @Schema(description = "订单ID")
    private String orderId;
    @Schema(description = "支付金额")
    private String amount;
    @Schema(description = "支付渠道")
    private String paymentChannel;
    @Schema(description = "支付状态")
    private String status;
    @Schema(description = "渠道交易号")
    private String channelTxNo;
    @Schema(description = "结果时间戳")
    private Long resultTime;

    public static PaymentResultEvent create(SysPaymentFlow paymentFlow, CreatePaymentRequest request, String status, String channelTxNo) {
        return PaymentResultEvent
                .builder()
                .paymentId(paymentFlow.getPaymentId())
                .userId(request.getUserId())
                .orderId(request.getOrderId())
                .amount(NumberUtils.formatDecimal(paymentFlow.getAmount()))
                .paymentChannel(PaymentChannel.fromCode(paymentFlow.getPayChannel()).getName())
                .status(status)
                .channelTxNo(channelTxNo)
                .resultTime(System.currentTimeMillis())
                .build();
    }
}
