package com.knet.payment.system.event;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/3 13:27
 * @description: 支付失败事件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentFailedEvent implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "支付流水ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "PAY-123456789012345678")
    private String paymentId;
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long userId;
    @Schema(description = "订单ID")
    private String orderId;
    @Schema(description = "支付金额")
    private String amount;
    @Schema(description = "支付渠道")
    private String paymentChannel;
    @Schema(description = "错误信息")
    private String errorMessage;
    @Schema(description = "失败时间戳")
    private Long failedTime;
}
