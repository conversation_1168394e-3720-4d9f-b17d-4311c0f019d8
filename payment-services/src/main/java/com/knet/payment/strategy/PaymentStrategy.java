package com.knet.payment.strategy;

import com.knet.common.enums.PaymentChannel;
import com.knet.payment.model.dto.req.CreatePaymentRequest;
import com.knet.payment.model.dto.res.CreatePaymentResponse;
import com.knet.payment.model.entity.SysPaymentFlow;

/**
 * <AUTHOR>
 * @date 2025/6/3 13:00
 * @description: 支付策略接口
 */
public interface PaymentStrategy {
    /**
     * 获取支持的支付渠道
     *
     * @return 支付渠道
     */
    PaymentChannel getSupportedChannel();

    /**
     * 处理支付
     *
     * @param paymentFlow     支付流水
     * @param request         支付请求
     * @param responseBuilder 响应构建器
     * @return 支付响应
     */
    CreatePaymentResponse processPayment(SysPaymentFlow paymentFlow,
                                         CreatePaymentRequest request,
                                         CreatePaymentResponse.CreatePaymentResponseBuilder responseBuilder);
}
