package com.knet.payment.strategy.impl;

import com.knet.common.enums.KnetPaymentFlowStatus;
import com.knet.common.enums.PaymentChannel;
import com.knet.common.enums.WalletRecordType;
import com.knet.common.exception.ServiceException;
import com.knet.payment.model.dto.req.CreatePaymentRequest;
import com.knet.payment.model.dto.res.CreatePaymentResponse;
import com.knet.payment.model.entity.SysPaymentFlow;
import com.knet.payment.service.ISysPaymentFlowService;
import com.knet.payment.service.ISysPaymentGroupService;
import com.knet.payment.service.ISysUserWalletService;
import com.knet.payment.service.ISysWalletRecordService;
import com.knet.payment.strategy.PaymentStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/3 13:05
 * @description: 钱包支付策略
 */
@Slf4j
@Component
public class WalletPaymentStrategy implements PaymentStrategy {
    @Resource
    private ISysUserWalletService userWalletService;
    @Resource
    private ISysWalletRecordService walletRecordService;
    @Resource
    private ISysPaymentFlowService paymentFlowService;
    @Resource
    private ISysPaymentGroupService paymentGroupService;

    @Override
    public PaymentChannel getSupportedChannel() {
        return PaymentChannel.WALLET;
    }

    @Override
    public CreatePaymentResponse processPayment(SysPaymentFlow paymentFlow,
                                                CreatePaymentRequest request,
                                                CreatePaymentResponse.CreatePaymentResponseBuilder responseBuilder) {
        Long userId = request.getUserId();
        BigDecimal amount = paymentFlow.getAmount();
        log.info("处理钱包支付: userId={}, amount={}", userId, amount);
        // 扣减钱包余额（使用服务方法）
        boolean updated = userWalletService.deductionBalance(userId, amount);
        if (!updated) {
            throw new ServiceException("钱包余额扣减失败");
        }
        // 创建钱包记录
        String recordId = walletRecordService.createWalletRecord(
                userId, amount, WalletRecordType.PAYMENT_DEDUCTION,
                paymentFlow.getPaymentId(), request.getOrderId(), request.getRemark());
        // 更新支付流水状态为成功
        paymentFlowService.paySuccess(paymentFlow.getPaymentId());
        // 更新支付组状态
        paymentGroupService.updatePaymentGroupStatus(paymentFlow.getGroupId(), amount);
        log.info("钱包支付成功: userId={}, amount={}, recordId={}", userId, amount, recordId);
        return responseBuilder
                .status(KnetPaymentFlowStatus.SUCCESS.getName())
                .build();
    }
}
