package com.knet.payment.strategy.impl;

import com.knet.common.enums.KnetPaymentFlowStatus;
import com.knet.common.enums.PaymentChannel;
import com.knet.payment.model.dto.req.CreatePaymentRequest;
import com.knet.payment.model.dto.res.CreatePaymentResponse;
import com.knet.payment.model.entity.SysPaymentFlow;
import com.knet.payment.service.ISysPaymentFlowService;
import com.knet.payment.strategy.PaymentStrategy;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/3 13:10
 * @description: 第三方支付策略抽象基类
 */
@Slf4j
public abstract class AbstractThirdPartyPaymentStrategy implements PaymentStrategy {

    @Resource
    protected ISysPaymentFlowService paymentFlowService;

    @Override
    public CreatePaymentResponse processPayment(SysPaymentFlow paymentFlow,
                                                CreatePaymentRequest request,
                                                CreatePaymentResponse.CreatePaymentResponseBuilder responseBuilder) {
        PaymentChannel channel = getSupportedChannel();
        log.info("处理第三方支付: channel={}, paymentId={}", channel.getName(), paymentFlow.getPaymentId());
        // 更新支付流水状态为支付中
        paymentFlow.setStatus(KnetPaymentFlowStatus.IN_PROGRESS);
        paymentFlowService.updateById(paymentFlow);
        // 调用具体的第三方支付接口
        String paymentUrl = callThirdPartyPaymentApi(paymentFlow, request);
        log.info("第三方支付创建成功: channel={}, paymentId={}, paymentUrl={}",
                channel.getName(), paymentFlow.getPaymentId(), paymentUrl);
        return responseBuilder
                .status(KnetPaymentFlowStatus.IN_PROGRESS.getName())
                .paymentUrl(paymentUrl)
                .build();
    }

    /**
     * 调用第三方支付接口
     * 子类需要实现具体的第三方支付逻辑
     *
     * @param paymentFlow 支付流水
     * @param request     支付请求
     * @return 支付链接或二维码
     */
    protected abstract String callThirdPartyPaymentApi(SysPaymentFlow paymentFlow, CreatePaymentRequest request);

    /**
     * 生成默认支付链接（模拟实现）
     *
     * @param paymentFlow 支付流水
     * @param channelName 渠道名称
     * @return 支付链接
     */
    protected String generateDefaultPaymentUrl(SysPaymentFlow paymentFlow, String channelName) {
        return String.format("https://pay.%s.com/pay?paymentId=%s",
                channelName.toLowerCase().replace("_", ""), paymentFlow.getPaymentId());
    }
}
