package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/15 14:05
 * @description: SAGA步骤状态枚举
 */
@Getter
@AllArgsConstructor
public enum SagaStepStatus {
    
    PENDING(0, "待执行"),
    RUNNING(1, "执行中"),
    COMPLETED(2, "已完成"),
    COMPENSATING(3, "补偿中"),
    COMPENSATED(4, "已补偿"),
    FAILED(5, "执行失败"),
    COMPENSATION_FAILED(6, "补偿失败");

    private final Integer code;
    
    @EnumValue
    @JsonValue
    private final String name;
}
