package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/15 14:00
 * @description: SAGA状态枚举
 */
@Getter
@AllArgsConstructor
public enum SagaStatus {
    
    RUNNING(0, "运行中"),
    COMPENSATING(1, "补偿中"),
    COMPLETED(2, "已完成"),
    FAILED(3, "已失败"),
    CANCELLED(4, "已取消");

    private final Integer code;
    
    @EnumValue
    @JsonValue
    private final String name;
}
