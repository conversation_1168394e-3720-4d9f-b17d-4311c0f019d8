package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/12 10:33
 * @description: knet订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum KnetOrderStatus {
    /**
     * 待支付
     */
    PENDING_PAYMENT(0, "PENDING_PAYMENT", "Pending Payment"),
    /**
     * 支付中
     */
    IN_PAYMENT(1, "IN_PAYMENT", "In Payment"),
    /**
     * 已支付
     */
    PAID(2, "PAID", "Paid"),
    /**
     * 部分成功
     */
    PARTIALLY_SUCCESSFUL(3, "PARTIALLY_SUCCESSFUL", "Partially Successful"),
    /**
     * 已取消
     */
    CANCELLED(4, "CANCELLED", "Cancelled");

    /**
     * 状态代码
     */
    private final Integer code;

    /**
     * 状态名称，用于数据库存储
     */
    @EnumValue
    @JsonValue
    private final String name;

    /**
     * 描述
     */
    private final String desc;

    public static KnetOrderStatus fromCode(int code) {
        for (KnetOrderStatus status : KnetOrderStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }
}
