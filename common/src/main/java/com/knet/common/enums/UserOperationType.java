package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/20 18:00
 * @description: 用户操作类型枚举
 */
@Getter
@AllArgsConstructor
public enum UserOperationType {

    // 用户账户相关操作
    /**
     * 用户注册
     */
    USER_REGISTER(1001, "USER_REGISTER", "用户注册"),
    /**
     * 用户登录
     */
    USER_LOGIN(1002, "USER_LOGIN", "用户登录"),
    /**
     * 用户登出
     */
    USER_LOGOUT(1003, "USER_LOGOUT", "用户登出"),
    /**
     * 修改密码
     */
    PASSWORD_CHANGE(1004, "PASSWORD_CHANGE", "修改密码"),
    /**
     * 修改个人信息
     */
    PROFILE_UPDATE(1005, "PROFILE_UPDATE", "修改个人信息"),

    // 钱包相关操作
    /**
     * 钱包充值
     */
    WALLET_RECHARGE(2001, "WALLET_RECHARGE", "钱包充值"),
    /**
     * 钱包提现
     */
    WALLET_WITHDRAW(2002, "WALLET_WITHDRAW", "钱包提现"),
    /**
     * 支付扣款
     */
    WALLET_PAYMENT(2003, "WALLET_PAYMENT", "支付扣款"),
    /**
     * 退款入账
     */
    WALLET_REFUND(2004, "WALLET_REFUND", "退款入账"),

    // 地址管理相关操作
    /**
     * 添加地址
     */
    ADDRESS_ADD(3001, "ADDRESS_ADD", "添加地址"),
    /**
     * 修改地址
     */
    ADDRESS_UPDATE(3002, "ADDRESS_UPDATE", "修改地址"),
    /**
     * 删除地址
     */
    ADDRESS_DELETE(3003, "ADDRESS_DELETE", "删除地址"),

    // 购物车相关操作
    /**
     * 添加商品到购物车
     */
    CART_ADD_ITEM(4001, "CART_ADD_ITEM", "添加商品到购物车"),
    /**
     * 修改购物车商品
     */
    CART_UPDATE_ITEM(4002, "CART_UPDATE_ITEM", "修改购物车商品"),
    /**
     * 删除购物车商品
     */
    CART_DELETE_ITEM(4003, "CART_DELETE_ITEM", "删除购物车商品"),
    /**
     * 清空购物车
     */
    CART_CLEAR(4004, "CART_CLEAR", "清空购物车"),

    // 订单相关操作
    /**
     * 创建订单
     */
    ORDER_CREATE(5001, "ORDER_CREATE", "创建订单"),
    /**
     * 取消订单
     */
    ORDER_CANCEL(5002, "ORDER_CANCEL", "取消订单"),
    /**
     * 支付订单
     */
    ORDER_PAYMENT(5003, "ORDER_PAYMENT", "支付订单"),
    /**
     * 确认收货
     */
    ORDER_CONFIRM(5004, "ORDER_CONFIRM", "确认收货"),

    // 系统管理操作
    /**
     * 管理员操作
     */
    ADMIN_OPERATION(9001, "ADMIN_OPERATION", "管理员操作"),
    /**
     * 系统维护
     */
    SYSTEM_MAINTENANCE(9002, "SYSTEM_MAINTENANCE", "系统维护");

    /**
     * 操作类型代码
     */
    private final Integer code;

    /**
     * 操作类型名称，用于数据库存储
     */
    @EnumValue
    @JsonValue
    private final String name;

    /**
     * 操作描述
     */
    private final String desc;

    public static UserOperationType fromCode(int code) {
        for (UserOperationType operationType : UserOperationType.values()) {
            if (operationType.getCode() == code) {
                return operationType;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }

    public static UserOperationType fromName(String name) {
        if (name == null) {
            return null;
        }
        for (UserOperationType operationType : UserOperationType.values()) {
            if (operationType.getName().equals(name)) {
                return operationType;
            }
        }
        throw new IllegalArgumentException("Unknown enum name " + name);
    }
}
