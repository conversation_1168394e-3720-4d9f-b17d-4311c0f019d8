package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/3 16:09
 * @description: 订单明细表-订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum KnetOrderItemStatus {
    /**
     * 待支付
     */
    PENDING_PAYMENT(0, "PENDING_PAYMENT", "Pending Payment"),
    /**
     * 已支付
     */
    PAID(1, "PAID", "Paid"),
    /**
     * 待发货
     */
    PENDING_SHIPMENT(2, "PENDING_SHIPMENT", "Pending Shipment"),
    /**
     * 物流在途
     */
    SHIPPED(3, "SHIPPED", "Shipped"),
    /**
     * 订单已取消
     */
    ORDER_CANCELLED(4, "ORDER_CANCELLED", "Order Cancelled"),
    /**
     * 订单已确认
     */
    ORDER_CONFORM(5, "ORDER_CONFORM", "Order Conform");
    /**
     * 状态代码
     */
    private final Integer code;

    /**
     * 状态名称，用于数据库存储
     */
    @EnumValue
    @JsonValue
    private final String name;

    /**
     * 描述
     */
    private final String desc;

    public static KnetOrderItemStatus fromCode(int code) {
        for (KnetOrderItemStatus status : KnetOrderItemStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }
}
