package com.knet.common.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/1/15 14:25
 * @description: SAGA补偿注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SagaCompensation {
    
    /**
     * 对应的正向步骤名称
     */
    String forStep();
    
    /**
     * 补偿方法名
     */
    String name() default "";
    
    /**
     * 超时时间（秒）
     */
    int timeout() default 30;
    
    /**
     * 描述
     */
    String description() default "";
}
