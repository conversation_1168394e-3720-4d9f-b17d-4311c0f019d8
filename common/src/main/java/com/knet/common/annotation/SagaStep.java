package com.knet.common.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/1/15 14:20
 * @description: SAGA步骤注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SagaStep {
    
    /**
     * 步骤名称
     */
    String name();
    
    /**
     * 步骤序号
     */
    int order() default 0;
    
    /**
     * 补偿方法名
     */
    String compensationMethod() default "";
    
    /**
     * 是否需要补偿
     */
    boolean needCompensation() default true;
    
    /**
     * 超时时间（秒）
     */
    int timeout() default 30;
    
    /**
     * 描述
     */
    String description() default "";
}
