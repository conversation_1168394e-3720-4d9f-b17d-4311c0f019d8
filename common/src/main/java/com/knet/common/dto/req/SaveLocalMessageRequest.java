package com.knet.common.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/15 11:35
 * @description: 保存本地消息请求 - 通用DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "保存本地消息请求")
public class SaveLocalMessageRequest extends BaseRequest {

    @NotBlank(message = "消息ID不能为空")
    @Schema(description = "消息ID，全局唯一", requiredMode = Schema.RequiredMode.REQUIRED)
    private String messageId;

    @NotBlank(message = "交换机名称不能为空")
    @Schema(description = "交换机名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String exchange;

    @NotBlank(message = "路由键不能为空")
    @Schema(description = "路由键", requiredMode = Schema.RequiredMode.REQUIRED)
    private String routingKey;

    @NotBlank(message = "消息体不能为空")
    @Schema(description = "消息体内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String messageBody;

    @Schema(description = "消息头信息")
    private Map<String, Object> messageHeaders;

    @Schema(description = "最大重试次数，默认3")
    @Builder.Default
    private Integer maxRetryCount = 3;

    @NotBlank(message = "业务类型不能为空")
    @Schema(description = "业务类型，如：order、payment、user等", requiredMode = Schema.RequiredMode.REQUIRED)
    private String businessType;

    @Schema(description = "业务ID，关联的业务主键")
    private String businessId;

    @Schema(description = "备注信息")
    private String remark;
}
