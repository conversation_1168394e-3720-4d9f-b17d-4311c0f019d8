package com.knet.common.utils;

import cn.hutool.core.util.RandomUtil;
import com.knet.common.dto.req.SaveLocalMessageRequest;
import com.knet.common.feign.LocalMessageFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/15 11:40
 * @description: 本地消息助手类 - 供其他服务使用
 */
@Slf4j
@Component
public class LocalMessageHelper {

    @Resource
    private LocalMessageFeignClient localMessageFeignClient;

    /**
     * 保存消息到本地消息表
     * 
     * @param exchange 交换机
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 是否保存成功
     */
    public boolean saveMessage(String exchange, String routingKey, String messageBody, 
                              String businessType, String businessId) {
        return saveMessage(exchange, routingKey, messageBody, businessType, businessId, null, null);
    }

    /**
     * 保存消息到本地消息表
     * 
     * @param exchange 交换机
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param headers 消息头
     * @param remark 备注
     * @return 是否保存成功
     */
    public boolean saveMessage(String exchange, String routingKey, String messageBody, 
                              String businessType, String businessId, 
                              Map<String, Object> headers, String remark) {
        try {
            String messageId = generateMessageId(businessType);
            
            if (headers == null) {
                headers = new HashMap<>();
            }
            headers.put("routingKey", routingKey);
            headers.put("messageId", messageId);

            SaveLocalMessageRequest request = SaveLocalMessageRequest.builder()
                    .messageId(messageId)
                    .exchange(exchange)
                    .routingKey(routingKey)
                    .messageBody(messageBody)
                    .messageHeaders(headers)
                    .businessType(businessType)
                    .businessId(businessId)
                    .remark(remark)
                    .build();

            localMessageFeignClient.saveLocalMessage(request);
            log.info("保存本地消息成功: messageId={}, businessType={}", messageId, businessType);
            return true;
            
        } catch (Exception e) {
            log.error("保存本地消息失败: businessType={}, businessId={}", businessType, businessId, e);
            return false;
        }
    }

    /**
     * 保存并立即发送消息
     * 
     * @param exchange 交换机
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 是否发送成功
     */
    public boolean saveAndSendMessage(String exchange, String routingKey, String messageBody, 
                                     String businessType, String businessId) {
        return saveAndSendMessage(exchange, routingKey, messageBody, businessType, businessId, null, null);
    }

    /**
     * 保存并立即发送消息
     * 
     * @param exchange 交换机
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param headers 消息头
     * @param remark 备注
     * @return 是否发送成功
     */
    public boolean saveAndSendMessage(String exchange, String routingKey, String messageBody, 
                                     String businessType, String businessId, 
                                     Map<String, Object> headers, String remark) {
        try {
            String messageId = generateMessageId(businessType);
            
            if (headers == null) {
                headers = new HashMap<>();
            }
            headers.put("routingKey", routingKey);
            headers.put("messageId", messageId);

            SaveLocalMessageRequest request = SaveLocalMessageRequest.builder()
                    .messageId(messageId)
                    .exchange(exchange)
                    .routingKey(routingKey)
                    .messageBody(messageBody)
                    .messageHeaders(headers)
                    .businessType(businessType)
                    .businessId(businessId)
                    .remark(remark)
                    .build();

            Boolean result = localMessageFeignClient.saveAndSendMessage(request).getData();
            log.info("保存并发送消息完成: messageId={}, businessType={}, 发送结果={}", 
                    messageId, businessType, result);
            return Boolean.TRUE.equals(result);
            
        } catch (Exception e) {
            log.error("保存并发送消息失败: businessType={}, businessId={}", businessType, businessId, e);
            return false;
        }
    }

    /**
     * 保存订单消息
     * 
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param orderId 订单ID
     * @return 是否保存成功
     */
    public boolean saveOrderMessage(String routingKey, String messageBody, String orderId) {
        return saveMessage("order-exchange", routingKey, messageBody, "order", orderId);
    }

    /**
     * 保存用户消息
     * 
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param userId 用户ID
     * @return 是否保存成功
     */
    public boolean saveUserMessage(String routingKey, String messageBody, String userId) {
        return saveMessage("user-operation-exchange", routingKey, messageBody, "user", userId);
    }

    /**
     * 保存支付消息
     * 
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param paymentId 支付ID
     * @return 是否保存成功
     */
    public boolean savePaymentMessage(String routingKey, String messageBody, String paymentId) {
        return saveMessage("payment-exchange", routingKey, messageBody, "payment", paymentId);
    }

    /**
     * 根据业务类型生成消息ID
     */
    private String generateMessageId(String businessType) {
        return String.format("KNET_B2B_%s_MSG_%s", 
                businessType.toUpperCase(), RandomUtil.randomString(16));
    }
}
