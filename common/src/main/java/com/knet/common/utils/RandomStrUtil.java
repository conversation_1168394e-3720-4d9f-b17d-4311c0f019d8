package com.knet.common.utils;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.knet.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.UnknownHostException;
import java.util.Enumeration;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/2/17 11:00
 * @description: 随机数工具
 */
@Slf4j
@Component
public class RandomStrUtil {
    /**
     * 雪花算法 snowflake
     */
    private final Snowflake snowflake;
    /**
     * 参数加密密钥
     */
    public static final String BIZ_DATA_KEY = "}<.kiLGQrn>(DgWsFa<lnbziGt$K#)_o";
    /**
     * 签名密钥
     */
    public static final String SIGN_KEY = "Nu.L{'rghQZ*UVlh)mU.oYt[$Jp)unmYIJfAm)lv}yQ-#ccqolU{dJyyNIPuj:KaOVK?TOYYUo.oFKQAneCCf%,CCnG>P(#AR{PS|Yx'_;S\">h{:deDU;zLgMBik&+zf";

    /**
     * 基于主机名和端口号生成唯一的workerId和datacenterId
     * todo 日后独立一个分布式号段服务
     *
     * @param env env
     */
    @Autowired
    public RandomStrUtil(Environment env) {
        StringBuilder uniqueIdBuilder = new StringBuilder();
        try {
            uniqueIdBuilder.append(InetAddress.getLocalHost().getHostName());
        } catch (UnknownHostException e) {
            uniqueIdBuilder.append(UUID.randomUUID());
            log.warn("无法获取主机名，使用随机UUID");
        }
        try {
            Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = netInterfaces.nextElement();
                if (!ni.isLoopback() && ni.isUp()) {
                    Enumeration<InetAddress> addresses = ni.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        InetAddress addr = addresses.nextElement();
                        if (addr instanceof Inet4Address) {
                            uniqueIdBuilder.append(":").append(addr.getHostAddress());
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("无法获取IP地址: {}", e.getMessage());
        }
        uniqueIdBuilder.append(":").append(env.getProperty("server.port", "8080"));
        String processId = ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
        uniqueIdBuilder.append(":").append(processId);
        uniqueIdBuilder.append(":").append(System.currentTimeMillis());
        String uniqueId = uniqueIdBuilder.toString();
        log.info("生成的唯一标识符: {}", uniqueId);
        // 生成workerId和datacenterId (0-31范围内)
        int combinedHash = Math.abs(uniqueId.hashCode());
        long workerId = combinedHash % 32;
        long datacenterId = (combinedHash / 32) % 32;
        this.snowflake = IdUtil.getSnowflake(workerId, datacenterId);
        log.info("初始化Snowflake ID生成器: workerId={}, datacenterId={}", workerId, datacenterId);
    }

    /**
     * 获取商品唯一ID
     * 雪花算法 18位
     *
     * @return productId 商品唯一ID
     */
    public String getProductId() {
        try {
            return "PROD-".concat(snowflake.nextIdStr());
        } catch (IllegalStateException e) {
            log.error("商品-时钟回拨异常: {}", e.getMessage());
            throw new ServiceException("ID生成服务不可用");
        }
    }

    /**
     * 获取订单唯一ID
     * 雪花算法 18位
     *
     * @return orderId 订单唯一ID
     */
    public String getOrderId() {
        try {
            return "ORD-".concat(snowflake.nextIdStr());
        } catch (IllegalStateException e) {
            log.error("订单-时钟回拨异常: {}", e.getMessage());
            throw new ServiceException("订单 ID生成服务不可用");
        }
    }

    /**
     * 获取订单唯一ID
     * 雪花算法 18位
     *
     * @return orderId 订单唯一ID
     */
    public String getSubOrderId() {
        try {
            return "ORDS-".concat(snowflake.nextIdStr());
        } catch (IllegalStateException e) {
            log.error("订单-时钟回拨 异常: {}", e.getMessage());
            throw new ServiceException("订单 SUB ID生成服务不可用");
        }
    }

    /**
     * 获取钱包充值记录ID
     * 雪花算法 18位
     *
     * @return walletRecordId 获取钱包充值记录ID
     */
    public String getWalletRecordId() {
        try {
            return "REC-".concat(snowflake.nextIdStr());
        } catch (IllegalStateException e) {
            log.error("钱包充值-时钟回拨异常: {}", e.getMessage());
            throw new ServiceException("钱包 ID生成服务不可用");
        }
    }

    /**
     * 获取操作ID
     * 雪花算法 18位
     *
     * @return operatedId 获取操作ID
     */
    public String getOperatedId() {
        try {
            return "OPC-".concat(snowflake.nextIdStr());
        } catch (IllegalStateException e) {
            log.error("operatedId-时钟回拨异常: {}", e.getMessage());
            throw new ServiceException("operatedId生成服务不可用");
        }
    }

    /**
     * 获取支付流水ID
     * 雪花算法 18位
     *
     * @return paymentId 支付流水ID
     */
    public String getPaymentId() {
        try {
            return "PAY-".concat(snowflake.nextIdStr());
        } catch (IllegalStateException e) {
            log.error("支付流水-时钟回拨异常: {}", e.getMessage());
            throw new ServiceException("支付流水ID生成服务不可用");
        }
    }

    /**
     * 获取支付组ID
     * 雪花算法 18位
     *
     * @return groupId 支付组ID
     */
    public String getPaymentGroupId() {
        try {
            return "GRP-".concat(snowflake.nextIdStr());
        } catch (IllegalStateException e) {
            log.error("支付组-时钟回拨异常: {}", e.getMessage());
            throw new ServiceException("支付组ID生成服务不可用");
        }
    }

    /**
     * biz-data 加密
     * AES/ECB/PKCS5Padding
     *
     * @param bizDataStr 待加密字符串
     * @return 加密后字符串
     */
    public String enCryptBizData(String bizDataStr) {
        if (StrUtil.isBlank(bizDataStr)) {
            throw new ServiceException("The string to be encrypted cannot be empty");
        }
        try {
            String encryptHex = SecureUtil.aes(BIZ_DATA_KEY.getBytes()).encryptHex(bizDataStr);
            return encryptHex;
        } catch (Exception e) {
            log.error("Encryption failed: {}", e.getMessage());
            throw new ServiceException("Encryption service is unavailable");
        }
    }

    /**
     * bizData 解密
     * * @param accessToken 令牌
     *
     * @param bizDataStr 待解密字符串
     * @return 解密后字符串
     */
    public String deCryptBizData(String bizDataStr) {
        if (StrUtil.isBlank(bizDataStr)) {
            throw new ServiceException("The string to be decrypted cannot be empty");
        }
        try {
            byte[] decrypt = SecureUtil.aes(BIZ_DATA_KEY.getBytes()).decrypt(bizDataStr);
            return new String(decrypt);
        } catch (Exception e) {
            log.error("Decryption failed: {}", e.getMessage());
            throw new ServiceException("Decryption service is unavailable");
        }
    }

    /**
     * 获取签名字符串
     *
     * @param accessToken 令牌
     * @param bizDataStr  业务数据
     * @return 签名字符串
     */
    public String getSignStr(String accessToken, String bizDataStr) {
        if (StrUtil.isBlank(accessToken)) {
            throw new ServiceException("accessToken cannot be empty");
        }
        try {
            String concatStr = accessToken.concat(bizDataStr);
            return SecureUtil.aes(SIGN_KEY.getBytes()).encryptHex(concatStr);
        } catch (Exception e) {
            log.error("签名字符串加密失败: {}", e.getMessage());
            throw new ServiceException("签名字符串加密失败");
        }
    }
}
