package com.knet.common.exception;

/**
 * <AUTHOR>
 * @date 2025/1/15 14:30
 * @description: SAGA异常基类
 */
public class SagaException extends RuntimeException {
    
    private String sagaId;
    private String stepName;
    
    public SagaException(String message) {
        super(message);
    }
    
    public SagaException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public SagaException(String sagaId, String stepName, String message) {
        super(message);
        this.sagaId = sagaId;
        this.stepName = stepName;
    }
    
    public SagaException(String sagaId, String stepName, String message, Throwable cause) {
        super(message, cause);
        this.sagaId = sagaId;
        this.stepName = stepName;
    }
    
    public String getSagaId() {
        return sagaId;
    }
    
    public String getStepName() {
        return stepName;
    }
}

/**
 * SAGA事务异常 - 需要回滚当前事务
 */
public class SagaTransactionException extends SagaException {
    
    public SagaTransactionException(String message) {
        super(message);
    }
    
    public SagaTransactionException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public SagaTransactionException(String sagaId, String stepName, String message) {
        super(sagaId, stepName, message);
    }
    
    public SagaTransactionException(String sagaId, String stepName, String message, Throwable cause) {
        super(sagaId, stepName, message, cause);
    }
}

/**
 * SAGA补偿异常 - 补偿失败
 */
public class SagaCompensationException extends SagaException {
    
    public SagaCompensationException(String message) {
        super(message);
    }
    
    public SagaCompensationException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public SagaCompensationException(String sagaId, String stepName, String message) {
        super(sagaId, stepName, message);
    }
    
    public SagaCompensationException(String sagaId, String stepName, String message, Throwable cause) {
        super(sagaId, stepName, message, cause);
    }
}
