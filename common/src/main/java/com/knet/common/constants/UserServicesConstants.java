package com.knet.common.constants;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/2/14 14:17
 * @description: 用户服务系统常量
 */
@Getter
public class UserServicesConstants {
    /**
     * 用户服务账号正则
     */
    public static final String USER_ACCOUNT_REGEX = "^[a-zA-Z0-9]{1,20}$";
    /**
     * 系统用户token key前缀
     */
    public static final String KNET_USER_TOKEN_PREFIX = "knet:b2b:token:userId:%s";
    /**
     * 系统用户token过期时间 24小时
     */
    public static final Long KNET_USER_TOKEN_EXPIRED_TIME = 24 * 60 * 60L;
    /**
     * 系统角色数据-过期时间 48小时
     */
    public static final Long KNET_ROLER_LIST_EXPIRED_TIME = 2 * 24 * 60 * 60L;
    /**
     * 系统用户uid key前缀
     */
    public static final String KNET_USER_UID_PREFIX = "knet:b2b:uid:%s";
    /**
     * 会用用户seesion key
     */
    public static final String MEMBER_USER = "MemberUser";
    /**
     * 角色列表
     */
    public static final String ROLER_LIST = "knet:b2b:roler:list";
    /**
     * 验证码
     */
    public static final String KNET_CAPTCHA_PREFIX = "knet:b2b:captcha:%s";

    /**
     * SKU图片缓存key前缀
     */
    public static final String SKU_IMG_CACHE_KEY_PREFIX = "sku:img:%s";
    /**
     * SKU缓存key前缀
     */
    public static final String KNET_SYS_SKU_CACHE_KEY_PREFIX = "knet:sys:sku:%s";
    /**
     * SKU 商品名称缓存key前缀
     */
    public static final String SKU_REMARKS_CACHE_KEY_PREFIX = "sku:remarks:%s";
    /**
     * 系统角色数据-过期时间 12小时
     */
    public static final Long KNET_BRANDS_EXPIRED_TIME = 12 * 60 * 60L;
    /**
     * 系统sku数据-过期时间 24小时
     */
    public static final Long KNET_SYS_SKU_EXPIRED_TIME = 24 * 60 * 60L;
    /**
     * user_shop_cart 用户购物车缓存key前缀
     */
    public static final String USER_SHOP_CART_CACHE_KEY_PREFIX = "knet:b2b:user_shop_cart:%s";
    /**
     * user_shop_cart_item 用户购物车特定商品缓存key前缀
     */
    public static final String USER_SHOP_CART_ITEM_CACHE_KEY_PREFIX = "knet:b2b:user_shop_cart_item:%s:%s:%s";
    /**
     * user_shop_cart 用户购物车缓存时间
     */
    public static final Long USER_SHOP_CART_CACHE_TIME = 30 * 60L;
}
