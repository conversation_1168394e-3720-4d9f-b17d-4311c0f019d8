package com.knet.common.feign;

import com.knet.common.base.HttpResult;
import com.knet.common.dto.req.SaveLocalMessageRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2025/1/15 11:30
 * @description: 本地消息表Feign客户端 - 供其他服务调用
 */
@FeignClient(name = "user-services", path = "/userServices/localMessage")
public interface LocalMessageFeignClient {

    /**
     * 保存本地消息
     * 
     * @param request 保存消息请求
     * @return 保存结果
     */
    @PostMapping("/save")
    HttpResult<Object> saveLocalMessage(@RequestBody SaveLocalMessageRequest request);

    /**
     * 保存消息并立即尝试发送
     * 
     * @param request 保存消息请求
     * @return 发送结果
     */
    @PostMapping("/saveAndSend")
    HttpResult<Boolean> saveAndSendMessage(@RequestBody SaveLocalMessageRequest request);
}
