# SAGA模式分布式事务实现

## 概述

本项目采用SAGA模式实现分布式事务，通过RabbitMQ进行事件驱动，将消息发送纳入本地事务控制，实现了简单可靠的分布式事务处理方案。

## 核心特性

### 1. 消息发送纳入事务
- **正向消息**：纳入当前事务，发送失败则事务回滚
- **补偿消息**：异步重试，确保最终成功
- **超时控制**：设置3秒超时，快速失败

### 2. SAGA状态管理
- **实例跟踪**：记录每个SAGA实例的执行状态
- **步骤日志**：详细记录每个步骤的执行过程
- **补偿机制**：自动触发补偿流程

### 3. 可靠性保证
- **乐观锁**：防止并发操作冲突
- **死信队列**：处理失败消息
- **重试机制**：支持自动和手动重试

## 架构设计

### 数据库表结构

```sql
-- SAGA实例表
CREATE TABLE `saga_instance` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `saga_id` varchar(64) NOT NULL COMMENT 'SAGA实例ID',
  `saga_type` varchar(50) NOT NULL COMMENT 'SAGA类型',
  `current_step` varchar(50) NOT NULL COMMENT '当前步骤',
  `status` varchar(20) NOT NULL COMMENT 'SAGA状态',
  `context_data` text COMMENT '上下文数据',
  `business_id` varchar(64) COMMENT '业务ID',
  `business_type` varchar(50) COMMENT '业务类型',
  -- 其他字段...
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_saga_id` (`saga_id`)
);

-- SAGA步骤日志表
CREATE TABLE `saga_step_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `saga_id` varchar(64) NOT NULL COMMENT 'SAGA实例ID',
  `step_name` varchar(50) NOT NULL COMMENT '步骤名称',
  `step_order` int(11) NOT NULL COMMENT '步骤序号',
  `status` varchar(20) NOT NULL COMMENT '步骤状态',
  `input_data` text COMMENT '输入数据',
  `output_data` text COMMENT '输出数据',
  -- 其他字段...
  PRIMARY KEY (`id`)
);
```

### 消息队列设计

```
order-exchange (订单交换机)
├── order.created.queue (订单创建队列)
└── order.failed.queue (订单失败队列)

saga-exchange (SAGA交换机)
├── saga.inventory.deduct (库存扣减)
├── saga.payment.process (支付处理)
├── saga.compensation.* (补偿消息)
└── saga.status.change (状态变更通知)
```

## 使用方式

### 1. 创建订单SAGA流程

```java
@Resource
private OrderSagaOrchestrator orderSagaOrchestrator;

// 创建订单SAGA
Map<String, Object> orderRequest = new HashMap<>();
orderRequest.put("userId", "USER_001");
orderRequest.put("productId", "PRODUCT_001");
orderRequest.put("quantity", 1);
orderRequest.put("amount", 100.00);

String sagaId = orderSagaOrchestrator.createOrderSaga(orderRequest);
```

### 2. 定义SAGA步骤

```java
@SagaStep(name = "createOrder", order = 1, compensationMethod = "cancelOrder")
@Transactional(rollbackFor = Exception.class)
public String createOrderSaga(Object orderRequest) {
    // 1. 创建SAGA实例
    SagaInstance sagaInstance = sagaManagerService.createSagaInstance(...);
    
    // 2. 执行业务逻辑
    String orderId = executeCreateOrder(orderRequest);
    
    // 3. 发送下一步消息（纳入事务）
    orderProducer.sendOrderCreateEvent(inventoryMessage, sagaId, "deductInventory");
    
    return sagaId;
}
```

### 3. 实现补偿方法

```java
@SagaCompensation(forStep = "createOrder")
public void cancelOrder(String sagaId, String compensationData) {
    // 执行订单取消逻辑
    executeCancelOrder(compensationData);
}
```

### 4. 消息消费处理

```java
@RabbitListener(queues = "order.created.queue")
public void handleOrderCreated(@Payload String messageBody, Message message, Channel channel) {
    try {
        String sagaId = (String) message.getMessageProperties().getHeaders().get("sagaId");
        String action = extractAction(messageBody);
        
        // 根据动作执行相应的SAGA步骤
        if ("deductInventory".equals(action)) {
            orderSagaOrchestrator.deductInventory(sagaId, messageData);
        }
        
        // 手动确认消息
        channel.basicAck(deliveryTag, false);
        
    } catch (Exception e) {
        // 拒绝消息，进入死信队列
        channel.basicNack(deliveryTag, false, false);
    }
}
```

## API接口

### 1. 创建订单SAGA
```
POST /orderService/saga/order/create
Content-Type: application/json

{
  "userId": "USER_001",
  "productId": "PRODUCT_001",
  "quantity": 1,
  "amount": 100.00
}
```

### 2. 查询SAGA实例
```
GET /orderService/saga/{sagaId}
```

### 3. 查询步骤日志
```
GET /orderService/saga/{sagaId}/steps
```

### 4. 触发补偿流程
```
POST /orderService/saga/{sagaId}/compensate?reason=手动触发补偿
```

### 5. 分页查询SAGA实例
```
GET /orderService/saga/page?current=1&size=10&sagaType=order_create&status=RUNNING
```

### 6. 获取统计信息
```
GET /orderService/saga/statistics
```

### 7. 测试接口
```
POST /orderService/saga/test/simple-order
```

## 配置说明

### 1. RabbitMQ配置

```yaml
spring:
  rabbitmq:
    host: ***********
    port: 5672
    username: guest
    password: guest
    publisher-confirm-type: correlated
    publisher-returns: true
    template:
      mandatory: true
      reply-timeout: 3000
```

### 2. 异步任务配置

```yaml
spring:
  task:
    execution:
      pool:
        core-size: 5
        max-size: 20
        queue-capacity: 100
        keep-alive: 60s
    scheduling:
      pool:
        size: 5
```

## 监控和运维

### 1. 日志监控

系统提供详细的日志记录：
- SAGA实例创建和状态变更
- 步骤执行过程和结果
- 消息发送和消费情况
- 补偿流程执行

### 2. 指标监控

建议监控以下指标：
- SAGA实例数量（按状态分类）
- 步骤执行成功率
- 消息发送成功率
- 补偿执行次数

### 3. 告警机制

建议设置以下告警：
- SAGA实例失败率过高
- 消息发送失败率过高
- 补偿执行失败
- 死信队列消息堆积

## 最佳实践

### 1. 事务边界控制

```java
@Transactional(rollbackFor = Exception.class)
public void sagaStep() {
    // 1. 执行业务逻辑
    executeBusinessLogic();
    
    // 2. 发送消息（纳入事务）
    sagaMessageSender.sendForwardMessage(...);
    
    // 如果消息发送失败，整个事务回滚
}
```

### 2. 超时时间设置

- **正向消息**：3秒超时，快速失败
- **补偿消息**：5秒超时，允许重试
- **业务操作**：根据具体业务设置合理超时

### 3. 幂等性设计

```java
public void processMessage(String messageId, String sagaId) {
    // 检查消息是否已处理
    if (isMessageProcessed(messageId)) {
        log.info("消息已处理，跳过: messageId={}", messageId);
        return;
    }
    
    // 处理消息
    doProcessMessage(sagaId);
    
    // 标记消息已处理
    markMessageProcessed(messageId);
}
```

### 4. 补偿设计原则

- **可补偿**：每个步骤都要有对应的补偿操作
- **幂等性**：补偿操作可以重复执行
- **向后兼容**：补偿逻辑要考虑数据状态变化

## 故障处理

### 1. 消息发送失败

- **现象**：事务回滚，业务操作未执行
- **处理**：检查MQ连接，修复后重新执行

### 2. 消息消费失败

- **现象**：消息进入死信队列
- **处理**：分析失败原因，修复后重新投递

### 3. 补偿失败

- **现象**：SAGA状态为补偿失败
- **处理**：手动检查数据状态，执行人工补偿

### 4. 数据不一致

- **现象**：SAGA完成但数据状态异常
- **处理**：通过步骤日志分析，执行数据修复

## 扩展性

### 1. 新增SAGA类型

1. 创建新的编排器类
2. 定义步骤和补偿方法
3. 配置消息队列
4. 实现消息消费者

### 2. 集成其他服务

1. 定义服务间消息格式
2. 实现Feign客户端调用
3. 处理服务调用失败场景

### 3. 支持其他MQ

1. 抽象消息发送接口
2. 实现不同MQ的适配器
3. 配置相应的连接参数

## 注意事项

1. **事务超时**：确保事务超时时间大于消息发送超时时间
2. **消息顺序**：同一SAGA的消息要保证顺序处理
3. **资源清理**：定期清理已完成的SAGA实例和步骤日志
4. **版本兼容**：升级时要考虑消息格式的向后兼容性
5. **性能优化**：合理设置连接池和线程池大小
