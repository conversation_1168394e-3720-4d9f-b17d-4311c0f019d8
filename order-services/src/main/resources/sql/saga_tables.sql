-- SAGA实例表
CREATE TABLE `saga_instance` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `saga_id` varchar(64) NOT NULL COMMENT 'SAGA实例ID，全局唯一',
  `saga_type` varchar(50) NOT NULL COMMENT 'SAGA类型，如：order_create、payment_process等',
  `current_step` varchar(50) NOT NULL COMMENT '当前执行步骤',
  `status` varchar(20) NOT NULL DEFAULT 'RUNNING' COMMENT 'SAGA状态：RUNNING-运行中，COMPENSATING-补偿中，COMPLETED-已完成，FAILED-已失败，CANCELLED-已取消',
  `context_data` text COMMENT '上下文数据，JSON格式',
  `business_id` varchar(64) DEFAULT NULL COMMENT '业务ID，关联的业务主键',
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型，如：order、payment等',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `failure_reason` varchar(500) DEFAULT NULL COMMENT '失败原因',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `max_retry_count` int(11) NOT NULL DEFAULT '3' COMMENT '最大重试次数',
  `next_retry_time` datetime DEFAULT NULL COMMENT '下次重试时间',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号，用于乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_saga_id` (`saga_id`),
  KEY `idx_saga_type` (`saga_type`),
  KEY `idx_status` (`status`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SAGA实例表';

-- SAGA步骤日志表
CREATE TABLE `saga_step_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `saga_id` varchar(64) NOT NULL COMMENT 'SAGA实例ID',
  `step_name` varchar(50) NOT NULL COMMENT '步骤名称',
  `step_order` int(11) NOT NULL COMMENT '步骤序号',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '步骤状态：PENDING-待执行，RUNNING-执行中，COMPLETED-已完成，COMPENSATING-补偿中，COMPENSATED-已补偿，FAILED-执行失败，COMPENSATION_FAILED-补偿失败',
  `input_data` text COMMENT '输入参数，JSON格式',
  `output_data` text COMMENT '输出结果，JSON格式',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` bigint(20) DEFAULT NULL COMMENT '执行耗时（毫秒）',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误信息',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `compensation_method` varchar(100) DEFAULT NULL COMMENT '补偿方法名',
  `compensation_data` text COMMENT '补偿参数，JSON格式',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号，用于乐观锁',
  PRIMARY KEY (`id`),
  KEY `idx_saga_id` (`saga_id`),
  KEY `idx_step_name` (`step_name`),
  KEY `idx_status` (`status`),
  KEY `idx_step_order` (`step_order`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SAGA步骤日志表';

-- 创建索引说明：
-- 1. uk_saga_id: SAGA实例ID唯一索引
-- 2. idx_saga_type: SAGA类型索引，用于按类型查询
-- 3. idx_status: 状态索引，用于查询特定状态的SAGA
-- 4. idx_business_id: 业务ID索引，用于按业务ID查询
-- 5. idx_business_type: 业务类型索引，用于按业务类型查询
-- 6. idx_step_name: 步骤名称索引，用于查询特定步骤
-- 7. idx_step_order: 步骤序号索引，用于按执行顺序查询
