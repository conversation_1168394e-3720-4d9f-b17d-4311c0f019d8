<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.order.mapper.SagaStepLogMapper">

    <!-- 查询SAGA的所有步骤日志 -->
    <select id="selectBySagaId" resultType="com.knet.order.model.entity.SagaStepLog">
        SELECT 
            id, saga_id, step_name, step_order, status, input_data, output_data,
            start_time, end_time, duration, error_message, retry_count,
            compensation_method, compensation_data, remark,
            create_time, update_time, del_flag, version
        FROM saga_step_log 
        WHERE saga_id = #{sagaId}
          AND del_flag = 0
        ORDER BY step_order ASC, create_time ASC
    </select>

    <!-- 查询SAGA的已完成步骤（用于补偿） -->
    <select id="selectCompletedStepsForCompensation" resultType="com.knet.order.model.entity.SagaStepLog">
        SELECT 
            id, saga_id, step_name, step_order, status, input_data, output_data,
            start_time, end_time, duration, error_message, retry_count,
            compensation_method, compensation_data, remark,
            create_time, update_time, del_flag, version
        FROM saga_step_log 
        WHERE saga_id = #{sagaId}
          AND del_flag = 0
          AND status = 'COMPLETED'
          AND compensation_method IS NOT NULL
          AND compensation_method != ''
        ORDER BY step_order DESC
    </select>

    <!-- 更新步骤状态 -->
    <update id="updateStepStatus">
        UPDATE saga_step_log 
        SET status = #{status},
            end_time = #{endTime},
            duration = #{duration},
            output_data = #{outputData},
            error_message = #{errorMessage},
            update_time = NOW(),
            version = version + 1
        WHERE id = #{id} 
          AND version = #{version}
          AND del_flag = 0
    </update>

    <!-- 增加步骤重试次数 -->
    <update id="incrementStepRetryCount">
        UPDATE saga_step_log 
        SET retry_count = retry_count + 1,
            update_time = NOW(),
            version = version + 1
        WHERE id = #{id} 
          AND version = #{version}
          AND del_flag = 0
    </update>

</mapper>
