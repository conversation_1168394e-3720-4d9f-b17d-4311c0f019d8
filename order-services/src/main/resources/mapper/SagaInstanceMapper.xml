<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.order.mapper.SagaInstanceMapper">

    <!-- 更新SAGA状态 -->
    <update id="updateSagaStatus">
        UPDATE saga_instance 
        SET status = #{status},
            current_step = #{currentStep},
            failure_reason = #{failureReason},
            end_time = #{endTime},
            update_time = NOW(),
            version = version + 1
        WHERE saga_id = #{sagaId} 
          AND version = #{version}
          AND del_flag = 0
    </update>

    <!-- 更新SAGA上下文数据 -->
    <update id="updateSagaContext">
        UPDATE saga_instance 
        SET context_data = #{contextData},
            update_time = NOW(),
            version = version + 1
        WHERE saga_id = #{sagaId} 
          AND version = #{version}
          AND del_flag = 0
    </update>

    <!-- 查询需要重试的SAGA实例 -->
    <select id="selectRetryableSagas" resultType="com.knet.order.model.entity.SagaInstance">
        SELECT 
            id, saga_id, saga_type, current_step, status, context_data,
            business_id, business_type, start_time, end_time, failure_reason,
            retry_count, max_retry_count, next_retry_time, remark,
            create_time, update_time, del_flag, version
        FROM saga_instance 
        WHERE del_flag = 0
          AND status = #{status}
          AND (next_retry_time IS NULL OR next_retry_time &lt;= #{currentTime})
          AND retry_count &lt; max_retry_count
        ORDER BY create_time ASC
        LIMIT #{limit}
    </select>

    <!-- 增加重试次数 -->
    <update id="incrementRetryCount">
        UPDATE saga_instance 
        SET retry_count = retry_count + 1,
            next_retry_time = #{nextRetryTime},
            update_time = NOW(),
            version = version + 1
        WHERE saga_id = #{sagaId} 
          AND version = #{version}
          AND del_flag = 0
    </update>

</mapper>
