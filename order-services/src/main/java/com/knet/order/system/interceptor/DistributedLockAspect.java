package com.knet.order.system.interceptor;

import cn.hutool.core.util.StrUtil;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/3/3 11:43
 * @description: 分布式锁切面
 */
@Slf4j
@Aspect
@Component
public class DistributedLockAspect {

    private static final String LOCK_PREFIX = "distributedLock:";
    private static final int MAX_KEY_LENGTH = 1024;
    private final ExpressionParser parser = new SpelExpressionParser();
    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();
    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Around("@annotation(distributedLock)")
    public Object lock(ProceedingJoinPoint joinPoint, DistributedLock distributedLock) throws Throwable {
        String lockKey = generateLockKey(joinPoint, distributedLock);
        String lockValue = UUID.randomUUID().toString();
        // 尝试加锁
        Boolean locked = redisCacheUtil.distributedLock(lockKey, lockValue, distributedLock.expire());
        if (locked != null && locked) {
            try {
                return joinPoint.proceed();
            } finally {
                redisCacheUtil.distributedUnlock(lockKey, lockValue);
            }
        } else {
            throw new ServiceException("orderService-获取分布式锁失败");
        }
    }

    /**
     * 生成锁的Key（支持SpEL表达式解析）
     */
    private String generateLockKey(ProceedingJoinPoint joinPoint, DistributedLock lock) {
        String key = lock.key();
        if (StrUtil.isEmpty(key)) {
            return LOCK_PREFIX + joinPoint.getSignature().toShortString();
        }
        if (key.contains("#")) {
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            Method method = methodSignature.getMethod();
            Object[] args = joinPoint.getArgs();
            // 解析SpEL表达式
            String parsedKey = parseSpEL(key, method, args);
            // 处理键长度限制
            if (parsedKey.length() > MAX_KEY_LENGTH) {
                log.warn("锁键过长 ({}), 将被截断", parsedKey.length());
                // 使用哈希值替代过长的键
                String hash = String.valueOf(parsedKey.hashCode());
                parsedKey = LOCK_PREFIX + method.getName() + ":" + hash;
            }
            return parsedKey;
        }
        return key;
    }

    /**
     * 解析SpEL表达式
     */
    private String parseSpEL(String spEL, Method method, Object[] args) {
        try {
            // 创建表达式
            Expression expression = parser.parseExpression(spEL);
            // 创建评估上下文
            EvaluationContext context = new StandardEvaluationContext();
            // 获取参数名称
            String[] parameterNames = parameterNameDiscoverer.getParameterNames(method);
            if (parameterNames != null) {
                // 将参数添加到上下文
                for (int i = 0; i < parameterNames.length; i++) {
                    context.setVariable(parameterNames[i], args[i]);
                }
            }
            // 评估表达式
            Object value = expression.getValue(context);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            log.error("解析SpEL表达式失败: {}, {}", spEL, e.getMessage(), e);
            throw e;
        }
    }
}