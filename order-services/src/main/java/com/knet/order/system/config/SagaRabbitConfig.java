package com.knet.order.system.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * <AUTHOR>
 * @date 2025/1/15 15:45
 * @description: SAGA模式RabbitMQ配置
 */
@Slf4j
@Configuration
public class SagaRabbitConfig {

    // ==================== 交换机定义 ====================

    @Bean
    public TopicExchange sagaExchange() {
        return ExchangeBuilder
                .topicExchange("saga-exchange")
                .durable(true)
                .build();
    }

    @Bean
    public TopicExchange orderExchange() {
        return ExchangeBuilder
                .topicExchange("order-exchange")
                .durable(true)
                .build();
    }

    // ==================== 队列定义 ====================

    @Bean
    public Queue orderCreatedQueue() {
        return QueueBuilder
                .durable("order.created.queue")
                .withArgument("x-dead-letter-exchange", "saga-dlx-exchange")
                .withArgument("x-dead-letter-routing-key", "order.created.dlq")
                .build();
    }

    @Bean
    public Queue inventoryDeductQueue() {
        return QueueBuilder
                .durable("inventory.deduct.queue")
                .withArgument("x-dead-letter-exchange", "saga-dlx-exchange")
                .withArgument("x-dead-letter-routing-key", "inventory.deduct.dlq")
                .build();
    }

    @Bean
    public Queue paymentProcessQueue() {
        return QueueBuilder
                .durable("payment.process.queue")
                .withArgument("x-dead-letter-exchange", "saga-dlx-exchange")
                .withArgument("x-dead-letter-routing-key", "payment.process.dlq")
                .build();
    }

    @Bean
    public Queue sagaCompensationQueue() {
        return QueueBuilder
                .durable("saga.compensation.queue")
                .withArgument("x-dead-letter-exchange", "saga-dlx-exchange")
                .withArgument("x-dead-letter-routing-key", "saga.compensation.dlq")
                .build();
    }

    @Bean
    public Queue sagaStatusChangeQueue() {
        return QueueBuilder
                .durable("saga.status.change.queue")
                .build();
    }

    // ==================== 死信队列 ====================

    @Bean
    public TopicExchange sagaDlxExchange() {
        return ExchangeBuilder
                .topicExchange("saga-dlx-exchange")
                .durable(true)
                .build();
    }

    @Bean
    public Queue orderCreatedDlq() {
        return QueueBuilder
                .durable("order.created.dlq")
                .build();
    }

    @Bean
    public Queue inventoryDeductDlq() {
        return QueueBuilder
                .durable("inventory.deduct.dlq")
                .build();
    }

    @Bean
    public Queue paymentProcessDlq() {
        return QueueBuilder
                .durable("payment.process.dlq")
                .build();
    }

    @Bean
    public Queue sagaCompensationDlq() {
        return QueueBuilder
                .durable("saga.compensation.dlq")
                .build();
    }

    // ==================== 绑定关系 ====================

    @Bean
    public Binding orderCreatedBinding() {
        return BindingBuilder
                .bind(orderCreatedQueue())
                .to(orderExchange())
                .with("order.created");
    }

    @Bean
    public Binding inventoryDeductBinding() {
        return BindingBuilder
                .bind(inventoryDeductQueue())
                .to(sagaExchange())
                .with("saga.inventory.deduct");
    }

    @Bean
    public Binding paymentProcessBinding() {
        return BindingBuilder
                .bind(paymentProcessQueue())
                .to(sagaExchange())
                .with("saga.payment.process");
    }

    @Bean
    public Binding sagaCompensationBinding() {
        return BindingBuilder
                .bind(sagaCompensationQueue())
                .to(sagaExchange())
                .with("saga.compensation.*");
    }

    @Bean
    public Binding sagaStatusChangeBinding() {
        return BindingBuilder
                .bind(sagaStatusChangeQueue())
                .to(sagaExchange())
                .with("saga.status.change");
    }

    // ==================== 死信绑定 ====================

    @Bean
    public Binding orderCreatedDlqBinding() {
        return BindingBuilder
                .bind(orderCreatedDlq())
                .to(sagaDlxExchange())
                .with("order.created.dlq");
    }

    @Bean
    public Binding inventoryDeductDlqBinding() {
        return BindingBuilder
                .bind(inventoryDeductDlq())
                .to(sagaDlxExchange())
                .with("inventory.deduct.dlq");
    }

    @Bean
    public Binding paymentProcessDlqBinding() {
        return BindingBuilder
                .bind(paymentProcessDlq())
                .to(sagaDlxExchange())
                .with("payment.process.dlq");
    }

    @Bean
    public Binding sagaCompensationDlqBinding() {
        return BindingBuilder
                .bind(sagaCompensationDlq())
                .to(sagaDlxExchange())
                .with("saga.compensation.dlq");
    }

    // ==================== RabbitTemplate配置 ====================

    @Bean
    @Primary
    public RabbitTemplate sagaRabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        
        // 设置较短的超时时间，快速失败
        template.setReplyTimeout(3000); // 3秒超时
        template.setMandatory(true); // 确保消息能路由到队列
        
        // 设置确认回调
        template.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.debug("消息发送成功: correlationData={}", correlationData);
            } else {
                log.error("消息发送失败: correlationData={}, cause={}", correlationData, cause);
            }
        });
        
        // 设置返回回调
        template.setReturnsCallback(returned -> {
            log.error("消息路由失败: message={}, replyCode={}, replyText={}, exchange={}, routingKey={}",
                    returned.getMessage(), returned.getReplyCode(), returned.getReplyText(),
                    returned.getExchange(), returned.getRoutingKey());
        });
        
        return template;
    }

    // ==================== 监听器容器工厂配置 ====================

    @Bean
    public SimpleRabbitListenerContainerFactory sagaRabbitListenerContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        
        // 设置并发消费者数量
        factory.setConcurrentConsumers(2);
        factory.setMaxConcurrentConsumers(10);
        
        // 设置预取数量
        factory.setPrefetchCount(10);
        
        // 设置确认模式
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        
        // 设置重试机制
        factory.setDefaultRequeueRejected(false); // 失败消息不重新入队，直接进入死信队列
        
        return factory;
    }
}
