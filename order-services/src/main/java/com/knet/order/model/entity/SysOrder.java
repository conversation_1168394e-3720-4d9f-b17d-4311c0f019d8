package com.knet.order.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.enums.KnetOrderStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/3 17:30
 * @description: B2B子订单表
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_order", description = "B2B子订单表")
@TableName("sys_order")
public class SysOrder extends BaseEntity {
    /**
     * 子订单ID（全局唯一）
     */
    @Schema(description = "子订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderId;

    /**
     * 母订单ID，关联母订单
     */
    @Schema(description = "母订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String parentOrderId;

    /**
     * 用户ID，关联企业账户
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    /**
     * 商品SKU
     */
    @Schema(description = "商品SKU", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sku;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productName;

    /**
     * 商品图片URL
     */
    @Schema(description = "商品图片URL")
    private String imageUrl;

    /**
     * 子订单总金额（含税价，单位：美元）
     */
    @Schema(description = "子订单总金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal totalAmount;

    /**
     * 商品总数量
     */
    @Schema(description = "商品总数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer totalQuantity;

    /**
     * 状态：0-待支付,1-支付中,2-已支付 3-部分成功 4-已取消
     *
     * @see com.knet.common.enums.KnetOrderStatus
     */
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private KnetOrderStatus status;

    public static SysOrder createSysOrder(String parentOrderId, Long userId, String subOrderId, String sku
            , String productName, String imageUrl, BigDecimal totalAmount, Integer totalQuantity) {
        return SysOrder
                .builder()
                .orderId(subOrderId)
                .parentOrderId(parentOrderId)
                .userId(userId)
                .sku(sku)
                .productName(productName)
                .imageUrl(imageUrl)
                .totalAmount(totalAmount)
                .totalQuantity(totalQuantity)
                .status(KnetOrderStatus.PENDING_PAYMENT)
                .build();
    }
}
