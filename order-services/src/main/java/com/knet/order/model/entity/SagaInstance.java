package com.knet.order.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.enums.SagaStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/15 14:10
 * @description: SAGA实例表
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "saga_instance", description = "SAGA实例表")
@TableName("saga_instance")
public class SagaInstance extends BaseEntity {

    @Schema(description = "SAGA实例ID，全局唯一", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sagaId;

    @Schema(description = "SAGA类型，如：order_create、payment_process等", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sagaType;

    @Schema(description = "当前执行步骤", requiredMode = Schema.RequiredMode.REQUIRED)
    private String currentStep;

    @Schema(description = "SAGA状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private SagaStatus status;

    @Schema(description = "上下文数据，JSON格式")
    private String contextData;

    @Schema(description = "业务ID，关联的业务主键")
    private String businessId;

    @Schema(description = "业务类型，如：order、payment等")
    private String businessType;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "失败原因")
    private String failureReason;

    @Schema(description = "重试次数")
    @Builder.Default
    private Integer retryCount = 0;

    @Schema(description = "最大重试次数")
    @Builder.Default
    private Integer maxRetryCount = 3;

    @Schema(description = "下次重试时间")
    private Date nextRetryTime;

    @Schema(description = "备注信息")
    private String remark;
}
