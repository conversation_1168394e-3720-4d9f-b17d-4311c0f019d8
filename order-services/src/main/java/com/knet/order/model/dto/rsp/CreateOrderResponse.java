package com.knet.order.model.dto.rsp;

import com.knet.common.enums.KnetOrderStatus;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.order.model.dto.SubOrderDataDto;
import com.knet.order.model.entity.SysOrder;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.entity.SysOrderItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/3 16:35
 * @description: 创建订单响应DTO（子母订单结构）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建订单响应")
public class CreateOrderResponse {

    @Schema(description = "母订单ID")
    private String parentOrderId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "母订单总金额（美元）")
    private String totalAmount;

    @Schema(description = "母订单状态")
    private KnetOrderStatus status;

    @Schema(description = "子订单列表")
    private List<SubOrderResponse> subOrders;

    /**
     * 子订单响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "子订单响应")
    public static class SubOrderResponse {

        @Schema(description = "子订单ID")
        private String orderId;

        @Schema(description = "商品SKU")
        private String sku;

        @Schema(description = "商品名称")
        private String productName;

        @Schema(description = "商品图片URL")
        private String imageUrl;

        @Schema(description = "子订单总金额（美元）")
        private String totalAmount;

        @Schema(description = "商品总数量")
        private Integer totalQuantity;

        @Schema(description = "子订单状态")
        private KnetOrderStatus status;

        @Schema(description = "订单明细项列表")
        private List<OrderItemResponse> items;
    }

    /**
     * 订单明细项响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "订单明细项响应")
    public static class OrderItemResponse {

        @Schema(description = "订单项ID")
        private Long itemId;

        @Schema(description = "商品SKU")
        private String sku;

        @Schema(description = "商品名称")
        private String name;

        @Schema(description = "尺码")
        private String size;

        @Schema(description = "单价（美元）")
        private String price;

        @Schema(description = "数量")
        private Integer count;

        @Schema(description = "小计金额（美元）")
        private String subtotal;
    }

    /**
     * 构建创建订单响应
     */
    public static CreateOrderResponse buildCreateOrderResponse(SysOrderGroup orderGroup, List<SubOrderDataDto> subOrderDataList) {
        List<CreateOrderResponse.SubOrderResponse> subOrderResponses = subOrderDataList.stream()
                .map(subOrderData -> {
                    SysOrder subOrder = subOrderData.getSubOrder();
                    List<SysOrderItem> orderItems = subOrderData.getOrderItems();
                    // 将订单明细按SKU+尺码+价格分组，统计数量用于响应展示
                    Map<String, List<SysOrderItem>> groupedItems = orderItems.stream()
                            .collect(Collectors.groupingBy(item ->
                                    item.getSku() + "|" + item.getSize() + "|" + item.getPrice().toString()));
                    List<CreateOrderResponse.OrderItemResponse> itemResponses = groupedItems.values()
                            .stream()
                            .map(items -> {
                                SysOrderItem firstItem = items.get(0);
                                int totalCount = items.size(); // 相同商品的记录数量就是总数量
                                BigDecimal subtotal = firstItem.getPrice().multiply(new BigDecimal(totalCount));
                                return CreateOrderResponse.OrderItemResponse.builder()
                                        .itemId(firstItem.getItemId())
                                        .sku(firstItem.getSku())
                                        .name(firstItem.getName())
                                        .size(firstItem.getSize())
                                        .price(PriceFormatUtil.formatPrice(firstItem.getPrice()))
                                        .count(totalCount)
                                        .subtotal(PriceFormatUtil.formatPrice(subtotal))
                                        .build();
                            })
                            .collect(Collectors.toList());
                    return CreateOrderResponse.SubOrderResponse.builder()
                            .orderId(subOrder.getOrderId())
                            .sku(subOrder.getSku())
                            .productName(subOrder.getProductName())
                            .imageUrl(subOrder.getImageUrl())
                            .totalAmount(PriceFormatUtil.formatPrice(subOrder.getTotalAmount()))
                            .totalQuantity(subOrder.getTotalQuantity())
                            .status(subOrder.getStatus())
                            .items(itemResponses)
                            .build();
                })
                .collect(Collectors.toList());
        return CreateOrderResponse.builder()
                .parentOrderId(orderGroup.getOrderId())
                .userId(orderGroup.getUserId())
                .totalAmount(PriceFormatUtil.formatPrice(orderGroup.getTotalAmount()))
                .status(orderGroup.getStatus())
                .subOrders(subOrderResponses)
                .build();
    }
}
