package com.knet.order.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.enums.SagaStepStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/15 14:15
 * @description: SAGA步骤日志表
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "saga_step_log", description = "SAGA步骤日志表")
@TableName("saga_step_log")
public class SagaStepLog extends BaseEntity {

    @Schema(description = "SAGA实例ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sagaId;

    @Schema(description = "步骤名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String stepName;

    @Schema(description = "步骤序号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer stepOrder;

    @Schema(description = "步骤状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private SagaStepStatus status;

    @Schema(description = "输入参数，JSON格式")
    private String inputData;

    @Schema(description = "输出结果，JSON格式")
    private String outputData;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "执行耗时（毫秒）")
    private Long duration;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "重试次数")
    @Builder.Default
    private Integer retryCount = 0;

    @Schema(description = "补偿方法名")
    private String compensationMethod;

    @Schema(description = "补偿参数，JSON格式")
    private String compensationData;

    @Schema(description = "备注信息")
    private String remark;
}
