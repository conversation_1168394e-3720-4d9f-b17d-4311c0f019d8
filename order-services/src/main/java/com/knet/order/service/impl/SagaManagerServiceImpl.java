package com.knet.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.SagaStatus;
import com.knet.common.enums.SagaStepStatus;
import com.knet.order.mapper.SagaInstanceMapper;
import com.knet.order.mapper.SagaStepLogMapper;
import com.knet.order.model.entity.SagaInstance;
import com.knet.order.model.entity.SagaStepLog;
import com.knet.order.saga.SagaMessageSender;
import com.knet.order.service.ISagaManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/1/15 15:05
 * @description: SAGA管理服务实现
 */
@Slf4j
@Service
public class SagaManagerServiceImpl extends ServiceImpl<SagaInstanceMapper, SagaInstance> 
        implements ISagaManagerService {

    @Resource
    private SagaInstanceMapper sagaInstanceMapper;
    
    @Resource
    private SagaStepLogMapper sagaStepLogMapper;
    
    @Resource
    private SagaMessageSender sagaMessageSender;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SagaInstance createSagaInstance(String sagaType, String businessId, String businessType, String contextData) {
        String sagaId = generateSagaId(sagaType);
        
        SagaInstance sagaInstance = SagaInstance.builder()
                .sagaId(sagaId)
                .sagaType(sagaType)
                .currentStep("START")
                .status(SagaStatus.RUNNING)
                .contextData(contextData)
                .businessId(businessId)
                .businessType(businessType)
                .startTime(new Date())
                .retryCount(0)
                .maxRetryCount(3)
                .build();

        boolean saved = this.save(sagaInstance);
        if (!saved) {
            throw new RuntimeException("创建SAGA实例失败: sagaId=" + sagaId);
        }

        log.info("SAGA实例创建成功: sagaId={}, sagaType={}, businessId={}", sagaId, sagaType, businessId);
        
        // 发送SAGA状态变更通知
        sagaMessageSender.sendSagaStatusChangeNotification(sagaId, null, SagaStatus.RUNNING.getName(), "SAGA实例创建");
        
        return sagaInstance;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SagaStepLog startSagaStep(String sagaId, String stepName, Integer stepOrder, 
                                    String inputData, String compensationMethod) {
        SagaStepLog stepLog = SagaStepLog.builder()
                .sagaId(sagaId)
                .stepName(stepName)
                .stepOrder(stepOrder)
                .status(SagaStepStatus.RUNNING)
                .inputData(inputData)
                .startTime(new Date())
                .retryCount(0)
                .compensationMethod(compensationMethod)
                .build();

        sagaStepLogMapper.insert(stepLog);
        
        log.info("SAGA步骤开始: sagaId={}, stepName={}, stepOrder={}", sagaId, stepName, stepOrder);
        return stepLog;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeSagaStep(Long stepLogId, String outputData) {
        SagaStepLog stepLog = sagaStepLogMapper.selectById(stepLogId);
        if (stepLog == null) {
            throw new RuntimeException("步骤日志不存在: stepLogId=" + stepLogId);
        }

        Date endTime = new Date();
        long duration = endTime.getTime() - stepLog.getStartTime().getTime();
        
        int updateResult = sagaStepLogMapper.updateStepStatus(
                stepLogId, SagaStepStatus.COMPLETED, endTime, duration, outputData, null, stepLog.getVersion()
        );
        
        if (updateResult == 0) {
            throw new RuntimeException("更新步骤状态失败，可能存在并发操作: stepLogId=" + stepLogId);
        }

        log.info("SAGA步骤完成: sagaId={}, stepName={}, duration={}ms", 
                stepLog.getSagaId(), stepLog.getStepName(), duration);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void failSagaStep(Long stepLogId, String errorMessage) {
        SagaStepLog stepLog = sagaStepLogMapper.selectById(stepLogId);
        if (stepLog == null) {
            throw new RuntimeException("步骤日志不存在: stepLogId=" + stepLogId);
        }

        Date endTime = new Date();
        long duration = endTime.getTime() - stepLog.getStartTime().getTime();
        
        int updateResult = sagaStepLogMapper.updateStepStatus(
                stepLogId, SagaStepStatus.FAILED, endTime, duration, null, errorMessage, stepLog.getVersion()
        );
        
        if (updateResult == 0) {
            throw new RuntimeException("更新步骤状态失败，可能存在并发操作: stepLogId=" + stepLogId);
        }

        log.error("SAGA步骤失败: sagaId={}, stepName={}, error={}", 
                stepLog.getSagaId(), stepLog.getStepName(), errorMessage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSagaStatus(String sagaId, SagaStatus status, String currentStep, String failureReason) {
        SagaInstance sagaInstance = getSagaInstance(sagaId);
        if (sagaInstance == null) {
            throw new RuntimeException("SAGA实例不存在: sagaId=" + sagaId);
        }

        SagaStatus oldStatus = sagaInstance.getStatus();
        Date endTime = (status == SagaStatus.COMPLETED || status == SagaStatus.FAILED || status == SagaStatus.CANCELLED) 
                ? new Date() : null;
        
        int updateResult = sagaInstanceMapper.updateSagaStatus(
                sagaId, status, currentStep, failureReason, endTime, sagaInstance.getVersion()
        );
        
        if (updateResult == 0) {
            throw new RuntimeException("更新SAGA状态失败，可能存在并发操作: sagaId=" + sagaId);
        }

        log.info("SAGA状态更新: sagaId={}, {} -> {}, currentStep={}", 
                sagaId, oldStatus.getName(), status.getName(), currentStep);
        
        // 发送状态变更通知
        sagaMessageSender.sendSagaStatusChangeNotification(
                sagaId, oldStatus.getName(), status.getName(), failureReason
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSagaContext(String sagaId, String contextData) {
        SagaInstance sagaInstance = getSagaInstance(sagaId);
        if (sagaInstance == null) {
            throw new RuntimeException("SAGA实例不存在: sagaId=" + sagaId);
        }

        int updateResult = sagaInstanceMapper.updateSagaContext(sagaId, contextData, sagaInstance.getVersion());
        
        if (updateResult == 0) {
            throw new RuntimeException("更新SAGA上下文失败，可能存在并发操作: sagaId=" + sagaId);
        }

        log.info("SAGA上下文更新: sagaId={}", sagaId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startCompensation(String sagaId, String failureReason) {
        // 更新SAGA状态为补偿中
        updateSagaStatus(sagaId, SagaStatus.COMPENSATING, "COMPENSATION", failureReason);
        
        // 获取需要补偿的步骤
        List<SagaStepLog> compensationSteps = getCompensationSteps(sagaId);
        
        if (CollUtil.isEmpty(compensationSteps)) {
            log.info("没有需要补偿的步骤: sagaId={}", sagaId);
            updateSagaStatus(sagaId, SagaStatus.CANCELLED, "COMPENSATION_COMPLETED", "无需补偿");
            return;
        }

        log.info("开始补偿流程: sagaId={}, 需要补偿的步骤数量={}", sagaId, compensationSteps.size());
        
        // 发送补偿消息（异步处理）
        for (SagaStepLog stepLog : compensationSteps) {
            try {
                String compensationMessage = buildCompensationMessage(stepLog);
                sagaMessageSender.sendCompensationMessage(
                        "saga-exchange", 
                        "saga.compensation." + stepLog.getStepName(),
                        compensationMessage,
                        sagaId,
                        stepLog.getStepName()
                );
            } catch (Exception e) {
                log.error("发送补偿消息失败: sagaId={}, stepName={}", sagaId, stepLog.getStepName(), e);
            }
        }
    }

    @Override
    public SagaInstance getSagaInstance(String sagaId) {
        LambdaQueryWrapper<SagaInstance> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SagaInstance::getSagaId, sagaId);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<SagaStepLog> getSagaStepLogs(String sagaId) {
        return sagaStepLogMapper.selectBySagaId(sagaId);
    }

    @Override
    public List<SagaStepLog> getCompensationSteps(String sagaId) {
        return sagaStepLogMapper.selectCompletedStepsForCompensation(sagaId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int processRetrySagas(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 100;
        }

        List<SagaInstance> retrySagas = sagaInstanceMapper.selectRetryableSagas(
                SagaStatus.FAILED, new Date(), limit
        );

        if (CollUtil.isEmpty(retrySagas)) {
            return 0;
        }

        int processedCount = 0;
        for (SagaInstance saga : retrySagas) {
            try {
                // 重新启动SAGA
                updateSagaStatus(saga.getSagaId(), SagaStatus.RUNNING, saga.getCurrentStep(), null);
                
                // 增加重试次数
                Date nextRetryTime = new Date(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(5));
                sagaInstanceMapper.incrementRetryCount(saga.getSagaId(), nextRetryTime, saga.getVersion());
                
                processedCount++;
                log.info("SAGA重试处理: sagaId={}, retryCount={}", saga.getSagaId(), saga.getRetryCount() + 1);
                
            } catch (Exception e) {
                log.error("SAGA重试处理失败: sagaId={}", saga.getSagaId(), e);
            }
        }

        return processedCount;
    }

    /**
     * 生成SAGA ID
     */
    private String generateSagaId(String sagaType) {
        return String.format("SAGA_%s_%s_%s", 
                sagaType.toUpperCase(), 
                System.currentTimeMillis(), 
                RandomUtil.randomString(8));
    }

    /**
     * 构建补偿消息
     */
    private String buildCompensationMessage(SagaStepLog stepLog) {
        return String.format(
                "{\"sagaId\":\"%s\",\"stepName\":\"%s\",\"compensationMethod\":\"%s\",\"compensationData\":%s,\"inputData\":%s}",
                stepLog.getSagaId(),
                stepLog.getStepName(),
                stepLog.getCompensationMethod(),
                stepLog.getCompensationData() != null ? stepLog.getCompensationData() : "null",
                stepLog.getInputData() != null ? stepLog.getInputData() : "null"
        );
    }
}
