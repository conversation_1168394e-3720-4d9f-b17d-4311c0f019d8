package com.knet.order.service;

import com.knet.common.enums.SagaStatus;
import com.knet.order.model.entity.SagaInstance;
import com.knet.order.model.entity.SagaStepLog;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/15 15:00
 * @description: SAGA管理服务接口
 */
public interface ISagaManagerService {

    /**
     * 创建SAGA实例
     * 
     * @param sagaType SAGA类型
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @param contextData 上下文数据
     * @return SAGA实例
     */
    SagaInstance createSagaInstance(String sagaType, String businessId, String businessType, String contextData);

    /**
     * 开始SAGA步骤
     * 
     * @param sagaId SAGA实例ID
     * @param stepName 步骤名称
     * @param stepOrder 步骤序号
     * @param inputData 输入数据
     * @param compensationMethod 补偿方法
     * @return 步骤日志
     */
    SagaStepLog startSagaStep(String sagaId, String stepName, Integer stepOrder, 
                             String inputData, String compensationMethod);

    /**
     * 完成SAGA步骤
     * 
     * @param stepLogId 步骤日志ID
     * @param outputData 输出数据
     */
    void completeSagaStep(Long stepLogId, String outputData);

    /**
     * 步骤执行失败
     * 
     * @param stepLogId 步骤日志ID
     * @param errorMessage 错误信息
     */
    void failSagaStep(Long stepLogId, String errorMessage);

    /**
     * 更新SAGA状态
     * 
     * @param sagaId SAGA实例ID
     * @param status 新状态
     * @param currentStep 当前步骤
     * @param failureReason 失败原因
     */
    void updateSagaStatus(String sagaId, SagaStatus status, String currentStep, String failureReason);

    /**
     * 更新SAGA上下文
     * 
     * @param sagaId SAGA实例ID
     * @param contextData 上下文数据
     */
    void updateSagaContext(String sagaId, String contextData);

    /**
     * 开始补偿流程
     * 
     * @param sagaId SAGA实例ID
     * @param failureReason 失败原因
     */
    void startCompensation(String sagaId, String failureReason);

    /**
     * 获取SAGA实例
     * 
     * @param sagaId SAGA实例ID
     * @return SAGA实例
     */
    SagaInstance getSagaInstance(String sagaId);

    /**
     * 获取SAGA步骤日志
     * 
     * @param sagaId SAGA实例ID
     * @return 步骤日志列表
     */
    List<SagaStepLog> getSagaStepLogs(String sagaId);

    /**
     * 获取需要补偿的步骤
     * 
     * @param sagaId SAGA实例ID
     * @return 需要补偿的步骤列表（倒序）
     */
    List<SagaStepLog> getCompensationSteps(String sagaId);

    /**
     * 处理重试的SAGA实例
     * 
     * @param limit 处理数量限制
     * @return 处理的数量
     */
    int processRetrySagas(Integer limit);
}
