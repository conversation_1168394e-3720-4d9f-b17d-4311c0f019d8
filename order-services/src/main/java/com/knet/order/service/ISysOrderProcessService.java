package com.knet.order.service;

import com.knet.order.model.dto.req.CreateOrderRequest;
import com.knet.order.model.dto.rsp.CreateOrderResponse;

/**
 * <AUTHOR>
 * @date 2025/6/4 14:16
 * @description: 订单聚合服务接口定义
 */
public interface ISysOrderProcessService {

    /**
     * 从购物车创建订单
     *
     * @param request 创建订单请求
     * @return 创建订单响应
     */
    CreateOrderResponse createOrderFromCart(CreateOrderRequest request);
}
