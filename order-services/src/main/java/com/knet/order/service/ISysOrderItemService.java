package com.knet.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.order.model.dto.OrderItemDataDto;
import com.knet.order.model.entity.SysOrderItem;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order_item(订单商品明细)】的数据库操作Service
 */
public interface ISysOrderItemService extends IService<SysOrderItem> {

    /**
     * 创建订单明细记录（每条记录数量为1，相同商品创建多条记录）
     *
     * @param subOrderId        子订单ID
     * @param orderItemDataList 订单商品数据
     * @return 订单明细列表
     */
    List<SysOrderItem> createOrderItems(String subOrderId, List<OrderItemDataDto> orderItemDataList);
}
