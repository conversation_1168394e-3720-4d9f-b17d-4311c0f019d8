package com.knet.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.order.mapper.SysOrderItemMapper;
import com.knet.order.model.dto.OrderItemDataDto;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.service.ISysOrderItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order_item(订单商品明细)】的数据库操作Service实现
 */
@Service
public class SysOrderItemServiceImpl extends ServiceImpl<SysOrderItemMapper, SysOrderItem> implements ISysOrderItemService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<SysOrderItem> createOrderItems(String subOrderId, List<OrderItemDataDto> orderItemDataList) {
        List<SysOrderItem> orderItems = new ArrayList<>();
        for (OrderItemDataDto itemData : orderItemDataList) {
            // 根据数量创建多条记录，每条记录的count都是1
            for (int i = 0; i < itemData.getQuantity(); i++) {
                SysOrderItem orderItem = new SysOrderItem();
                orderItem.setOrderId(subOrderId);
                orderItem.setSku(itemData.getSku());
                orderItem.setSize(itemData.getSize());
                orderItem.setName(itemData.getProductName());
                orderItem.setPrice(itemData.getUnitPrice());
                orderItem.setCount(1);
                orderItem.setStatus(KnetOrderItemStatus.PENDING_PAYMENT);
                // todo 如果有oneId字段，可以在这里设置
                orderItems.add(orderItem);
            }
        }
        this.saveBatch(orderItems);
        return orderItems;
    }
}
