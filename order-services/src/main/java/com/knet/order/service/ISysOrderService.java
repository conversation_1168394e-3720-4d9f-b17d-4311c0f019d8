package com.knet.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.order.model.dto.OrderItemDataDto;
import com.knet.order.model.entity.SysOrder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3 17:35
 * @description: 针对表【sys_order(B2B子订单表)】的数据库操作Service
 */
public interface ISysOrderService extends IService<SysOrder> {

    /**
     * 创建子订单
     *
     * @param parentOrderId 母订单ID
     * @param userId        用户ID
     * @param sku           商品SKU
     * @param itemDataList  商品明细数据
     * @return 子订单
     */
    SysOrder createSysOrder(String parentOrderId, Long userId, String sku, List<OrderItemDataDto> itemDataList);
}
