package com.knet.order.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.knet.order.saga.OrderSagaOrchestrator;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/15 16:00
 * @description: SAGA消息消费者
 */
@Slf4j
@Component
public class SagaMessageConsumer {

    @Resource
    private OrderSagaOrchestrator orderSagaOrchestrator;

    /**
     * 处理订单创建消息
     */
    @RabbitListener(queues = "order.created.queue", containerFactory = "sagaRabbitListenerContainerFactory")
    public void handleOrderCreated(@Payload String messageBody,
                                  Message message,
                                  Channel channel,
                                  @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            log.info("接收到订单创建消息: {}", messageBody);
            
            // 解析消息
            Map<String, Object> messageData = JSON.parseObject(messageBody, Map.class);
            String action = (String) messageData.get("action");
            String sagaId = (String) message.getMessageProperties().getHeaders().get("sagaId");
            
            if ("deductInventory".equals(action)) {
                // 处理库存扣减
                orderSagaOrchestrator.deductInventory(sagaId, messageData);
            } else if ("processPayment".equals(action)) {
                // 处理支付
                orderSagaOrchestrator.processPayment(sagaId, messageData);
            } else {
                log.warn("未知的消息动作: action={}, sagaId={}", action, sagaId);
            }
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            log.info("订单创建消息处理完成: sagaId={}, action={}", sagaId, action);
            
        } catch (Exception e) {
            log.error("处理订单创建消息失败: messageBody={}, error={}", messageBody, e.getMessage(), e);
            
            try {
                // 拒绝消息，不重新入队（进入死信队列）
                channel.basicNack(deliveryTag, false, false);
            } catch (IOException ioException) {
                log.error("拒绝消息失败: {}", ioException.getMessage(), ioException);
            }
        }
    }

    /**
     * 处理库存扣减消息
     */
    @RabbitListener(queues = "inventory.deduct.queue", containerFactory = "sagaRabbitListenerContainerFactory")
    public void handleInventoryDeduct(@Payload String messageBody,
                                     Message message,
                                     Channel channel,
                                     @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            log.info("接收到库存扣减消息: {}", messageBody);
            
            String sagaId = (String) message.getMessageProperties().getHeaders().get("sagaId");
            Map<String, Object> messageData = JSON.parseObject(messageBody, Map.class);
            
            // 处理库存扣减
            orderSagaOrchestrator.deductInventory(sagaId, messageData);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            log.info("库存扣减消息处理完成: sagaId={}", sagaId);
            
        } catch (Exception e) {
            log.error("处理库存扣减消息失败: messageBody={}, error={}", messageBody, e.getMessage(), e);
            
            try {
                // 拒绝消息，不重新入队（进入死信队列）
                channel.basicNack(deliveryTag, false, false);
            } catch (IOException ioException) {
                log.error("拒绝消息失败: {}", ioException.getMessage(), ioException);
            }
        }
    }

    /**
     * 处理支付消息
     */
    @RabbitListener(queues = "payment.process.queue", containerFactory = "sagaRabbitListenerContainerFactory")
    public void handlePaymentProcess(@Payload String messageBody,
                                    Message message,
                                    Channel channel,
                                    @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            log.info("接收到支付处理消息: {}", messageBody);
            
            String sagaId = (String) message.getMessageProperties().getHeaders().get("sagaId");
            Map<String, Object> messageData = JSON.parseObject(messageBody, Map.class);
            
            // 处理支付
            orderSagaOrchestrator.processPayment(sagaId, messageData);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            log.info("支付处理消息处理完成: sagaId={}", sagaId);
            
        } catch (Exception e) {
            log.error("处理支付消息失败: messageBody={}, error={}", messageBody, e.getMessage(), e);
            
            try {
                // 拒绝消息，不重新入队（进入死信队列）
                channel.basicNack(deliveryTag, false, false);
            } catch (IOException ioException) {
                log.error("拒绝消息失败: {}", ioException.getMessage(), ioException);
            }
        }
    }

    /**
     * 处理补偿消息
     */
    @RabbitListener(queues = "saga.compensation.queue", containerFactory = "sagaRabbitListenerContainerFactory")
    public void handleCompensation(@Payload String messageBody,
                                  Message message,
                                  Channel channel,
                                  @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            log.info("接收到补偿消息: {}", messageBody);
            
            String sagaId = (String) message.getMessageProperties().getHeaders().get("sagaId");
            String stepName = (String) message.getMessageProperties().getHeaders().get("stepName");
            
            Map<String, Object> compensationData = JSON.parseObject(messageBody, Map.class);
            String compensationMethod = (String) compensationData.get("compensationMethod");
            
            // 根据补偿方法执行相应的补偿逻辑
            executeCompensation(sagaId, stepName, compensationMethod, messageBody);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            log.info("补偿消息处理完成: sagaId={}, stepName={}, compensationMethod={}", 
                    sagaId, stepName, compensationMethod);
            
        } catch (Exception e) {
            log.error("处理补偿消息失败: messageBody={}, error={}", messageBody, e.getMessage(), e);
            
            try {
                // 补偿消息失败，重新入队重试
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ioException) {
                log.error("拒绝消息失败: {}", ioException.getMessage(), ioException);
            }
        }
    }

    /**
     * 处理SAGA状态变更通知
     */
    @RabbitListener(queues = "saga.status.change.queue", containerFactory = "sagaRabbitListenerContainerFactory")
    public void handleSagaStatusChange(@Payload String messageBody,
                                      Message message,
                                      Channel channel,
                                      @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            log.info("接收到SAGA状态变更通知: {}", messageBody);
            
            Map<String, Object> statusData = JSON.parseObject(messageBody, Map.class);
            String sagaId = (String) statusData.get("sagaId");
            String oldStatus = (String) statusData.get("oldStatus");
            String newStatus = (String) statusData.get("newStatus");
            String reason = (String) statusData.get("reason");
            
            // 处理状态变更通知（如发送邮件、更新缓存等）
            handleStatusChangeNotification(sagaId, oldStatus, newStatus, reason);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            log.info("SAGA状态变更通知处理完成: sagaId={}, {} -> {}", sagaId, oldStatus, newStatus);
            
        } catch (Exception e) {
            log.error("处理SAGA状态变更通知失败: messageBody={}, error={}", messageBody, e.getMessage(), e);
            
            try {
                // 状态变更通知失败，不重新入队
                channel.basicNack(deliveryTag, false, false);
            } catch (IOException ioException) {
                log.error("拒绝消息失败: {}", ioException.getMessage(), ioException);
            }
        }
    }

    /**
     * 执行补偿逻辑
     */
    private void executeCompensation(String sagaId, String stepName, String compensationMethod, String compensationData) {
        try {
            switch (compensationMethod) {
                case "cancelOrder":
                    orderSagaOrchestrator.cancelOrder(sagaId, compensationData);
                    break;
                case "restoreInventory":
                    orderSagaOrchestrator.restoreInventory(sagaId, compensationData);
                    break;
                case "refundPayment":
                    orderSagaOrchestrator.refundPayment(sagaId, compensationData);
                    break;
                default:
                    log.warn("未知的补偿方法: compensationMethod={}, sagaId={}, stepName={}", 
                            compensationMethod, sagaId, stepName);
            }
        } catch (Exception e) {
            log.error("执行补偿逻辑失败: sagaId={}, stepName={}, compensationMethod={}, error={}", 
                    sagaId, stepName, compensationMethod, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理状态变更通知
     */
    private void handleStatusChangeNotification(String sagaId, String oldStatus, String newStatus, String reason) {
        // 这里可以实现具体的通知逻辑，如：
        // 1. 发送邮件通知
        // 2. 更新缓存
        // 3. 记录审计日志
        // 4. 触发其他业务逻辑
        
        log.info("处理SAGA状态变更: sagaId={}, {} -> {}, reason={}", sagaId, oldStatus, newStatus, reason);
        
        // 示例：根据状态执行不同的通知逻辑
        switch (newStatus) {
            case "已完成":
                log.info("SAGA流程成功完成，发送成功通知: sagaId={}", sagaId);
                break;
            case "已失败":
                log.warn("SAGA流程失败，发送失败告警: sagaId={}, reason={}", sagaId, reason);
                break;
            case "已取消":
                log.info("SAGA流程已取消，发送取消通知: sagaId={}, reason={}", sagaId, reason);
                break;
            default:
                log.debug("SAGA状态变更: sagaId={}, newStatus={}", sagaId, newStatus);
        }
    }
}
