package com.knet.order.saga;

import com.knet.common.exception.SagaException;
import com.knet.common.exception.SagaTransactionException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/1/15 14:35
 * @description: SAGA消息发送器 - 支持事务内发送和异步补偿发送
 */
@Slf4j
@Component
public class SagaMessageSender {

    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送正向流程消息（纳入当前事务）
     * 消息发送失败会抛出异常，导致事务回滚
     * 
     * @param exchange 交换机
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param sagaId SAGA实例ID
     * @param stepName 步骤名称
     */
    public void sendForwardMessage(String exchange, String routingKey, String messageBody, 
                                  String sagaId, String stepName) {
        try {
            log.info("发送正向消息: sagaId={}, stepName={}, routingKey={}", sagaId, stepName, routingKey);
            
            // 设置较短的超时时间，快速失败
            RabbitTemplate template = createRabbitTemplate(3000); // 3秒超时
            
            // 构建消息属性
            MessageProperties properties = new MessageProperties();
            properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            properties.setHeader("sagaId", sagaId);
            properties.setHeader("stepName", stepName);
            properties.setHeader("routingKey", routingKey);
            properties.setHeader("messageType", "FORWARD");
            
            Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
            
            // 发送消息并等待确认
            CorrelationData correlationData = new CorrelationData(sagaId + "_" + stepName);
            template.convertAndSend(exchange, routingKey, message, correlationData);
            
            // 同步等待确认结果
            CorrelationData.Confirm confirm = correlationData.getFuture().get(3, TimeUnit.SECONDS);
            if (confirm == null || !confirm.isAck()) {
                throw new SagaException(sagaId, stepName, "消息未到达Broker");
            }
            
            log.info("正向消息发送成功: sagaId={}, stepName={}", sagaId, stepName);
            
        } catch (Exception e) {
            log.error("正向消息发送失败: sagaId={}, stepName={}, error={}", sagaId, stepName, e.getMessage());
            throw new SagaTransactionException(sagaId, stepName, "正向消息发送失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送补偿消息（异步重试，确保最终成功）
     * 
     * @param exchange 交换机
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param sagaId SAGA实例ID
     * @param stepName 步骤名称
     */
    @Async
    @Retryable(value = Exception.class, maxAttempts = 5, backoff = @Backoff(delay = 1000, multiplier = 2))
    public void sendCompensationMessage(String exchange, String routingKey, String messageBody, 
                                       String sagaId, String stepName) {
        try {
            log.info("发送补偿消息: sagaId={}, stepName={}, routingKey={}", sagaId, stepName, routingKey);
            
            // 构建消息属性
            MessageProperties properties = new MessageProperties();
            properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            properties.setHeader("sagaId", sagaId);
            properties.setHeader("stepName", stepName);
            properties.setHeader("routingKey", routingKey);
            properties.setHeader("messageType", "COMPENSATION");
            
            Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
            
            // 发送消息
            CorrelationData correlationData = new CorrelationData(sagaId + "_compensation_" + stepName);
            rabbitTemplate.convertAndSend(exchange, routingKey, message, correlationData);
            
            // 等待确认
            CorrelationData.Confirm confirm = correlationData.getFuture().get(5, TimeUnit.SECONDS);
            if (confirm == null || !confirm.isAck()) {
                throw new RuntimeException("补偿消息未到达Broker");
            }
            
            log.info("补偿消息发送成功: sagaId={}, stepName={}", sagaId, stepName);
            
        } catch (Exception e) {
            log.error("补偿消息发送失败: sagaId={}, stepName={}, error={}", sagaId, stepName, e.getMessage());
            throw new RuntimeException("补偿消息发送失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送SAGA状态变更通知消息
     * 
     * @param sagaId SAGA实例ID
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     * @param reason 变更原因
     */
    @Async
    public void sendSagaStatusChangeNotification(String sagaId, String oldStatus, String newStatus, String reason) {
        try {
            log.info("发送SAGA状态变更通知: sagaId={}, {} -> {}, reason={}", sagaId, oldStatus, newStatus, reason);
            
            String messageBody = String.format(
                "{\"sagaId\":\"%s\",\"oldStatus\":\"%s\",\"newStatus\":\"%s\",\"reason\":\"%s\",\"timestamp\":%d}",
                sagaId, oldStatus, newStatus, reason, System.currentTimeMillis()
            );
            
            MessageProperties properties = new MessageProperties();
            properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            properties.setHeader("sagaId", sagaId);
            properties.setHeader("messageType", "STATUS_CHANGE");
            
            Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
            
            rabbitTemplate.convertAndSend("saga-exchange", "saga.status.change", message);
            
            log.info("SAGA状态变更通知发送成功: sagaId={}", sagaId);
            
        } catch (Exception e) {
            log.error("SAGA状态变更通知发送失败: sagaId={}, error={}", sagaId, e.getMessage());
            // 状态变更通知失败不影响主流程，只记录日志
        }
    }

    /**
     * 创建带有自定义超时时间的RabbitTemplate
     */
    private RabbitTemplate createRabbitTemplate(int timeoutMs) {
        RabbitTemplate template = new RabbitTemplate(rabbitTemplate.getConnectionFactory());
        template.setReplyTimeout(timeoutMs);
        template.setMandatory(true); // 确保消息能路由到队列
        template.setConfirmCallback(rabbitTemplate.getConfirmCallback());
        template.setReturnsCallback(rabbitTemplate.getReturnsCallback());
        return template;
    }
}
