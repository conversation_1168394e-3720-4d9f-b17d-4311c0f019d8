package com.knet.order.saga;

import com.alibaba.fastjson2.JSON;
import com.knet.common.annotation.SagaCompensation;
import com.knet.common.annotation.SagaStep;
import com.knet.common.enums.SagaStatus;
import com.knet.common.exception.SagaTransactionException;
import com.knet.order.model.entity.SagaInstance;
import com.knet.order.model.entity.SagaStepLog;
import com.knet.order.mq.producer.OrderProducer;
import com.knet.order.service.ISagaManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/15 15:30
 * @description: 订单SAGA编排器
 */
@Slf4j
@Component
public class OrderSagaOrchestrator {

    @Resource
    private ISagaManagerService sagaManagerService;
    
    @Resource
    private OrderProducer orderProducer;

    /**
     * 创建订单SAGA流程
     * 
     * @param orderRequest 订单请求
     * @return SAGA实例ID
     */
    @SagaStep(name = "createOrder", order = 1, compensationMethod = "cancelOrder")
    @Transactional(rollbackFor = Exception.class)
    public String createOrderSaga(Object orderRequest) {
        try {
            log.info("开始创建订单SAGA流程: orderRequest={}", orderRequest);
            
            // 1. 创建SAGA实例
            String contextData = JSON.toJSONString(Map.of("orderRequest", orderRequest));
            SagaInstance sagaInstance = sagaManagerService.createSagaInstance(
                    "order_create", 
                    extractOrderId(orderRequest), 
                    "order", 
                    contextData
            );
            
            String sagaId = sagaInstance.getSagaId();
            
            // 2. 开始第一个步骤：创建订单
            SagaStepLog stepLog = sagaManagerService.startSagaStep(
                    sagaId, 
                    "createOrder", 
                    1, 
                    JSON.toJSONString(orderRequest), 
                    "cancelOrder"
            );
            
            // 3. 执行订单创建业务逻辑
            String orderId = executeCreateOrder(orderRequest);
            
            // 4. 完成步骤
            sagaManagerService.completeSagaStep(stepLog.getId(), JSON.toJSONString(Map.of("orderId", orderId)));
            
            // 5. 发送下一步消息（库存扣减）- 纳入事务
            String inventoryMessage = buildInventoryDeductMessage(orderId, orderRequest);
            orderProducer.sendOrderCreateEvent(inventoryMessage, sagaId, "deductInventory");
            
            log.info("订单SAGA流程第一步完成: sagaId={}, orderId={}", sagaId, orderId);
            return sagaId;
            
        } catch (Exception e) {
            log.error("创建订单SAGA流程失败: orderRequest={}, error={}", orderRequest, e.getMessage(), e);
            throw new SagaTransactionException("createOrder", "创建订单SAGA流程失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理库存扣减步骤
     * 
     * @param sagaId SAGA实例ID
     * @param inventoryRequest 库存扣减请求
     */
    @SagaStep(name = "deductInventory", order = 2, compensationMethod = "restoreInventory")
    @Transactional(rollbackFor = Exception.class)
    public void deductInventory(String sagaId, Object inventoryRequest) {
        try {
            log.info("处理库存扣减: sagaId={}, inventoryRequest={}", sagaId, inventoryRequest);
            
            // 1. 开始步骤
            SagaStepLog stepLog = sagaManagerService.startSagaStep(
                    sagaId, 
                    "deductInventory", 
                    2, 
                    JSON.toJSONString(inventoryRequest), 
                    "restoreInventory"
            );
            
            // 2. 执行库存扣减业务逻辑
            String deductResult = executeInventoryDeduct(inventoryRequest);
            
            // 3. 完成步骤
            sagaManagerService.completeSagaStep(stepLog.getId(), deductResult);
            
            // 4. 发送下一步消息（支付处理）- 纳入事务
            String paymentMessage = buildPaymentMessage(sagaId, inventoryRequest);
            orderProducer.sendOrderCreateEvent(paymentMessage, sagaId, "processPayment");
            
            log.info("库存扣减完成: sagaId={}", sagaId);
            
        } catch (Exception e) {
            log.error("库存扣减失败: sagaId={}, error={}", sagaId, e.getMessage(), e);
            
            // 记录步骤失败
            sagaManagerService.failSagaStep(getSagaStepLogId(sagaId, "deductInventory"), e.getMessage());
            
            // 开始补偿流程
            sagaManagerService.startCompensation(sagaId, "库存扣减失败: " + e.getMessage());
            
            throw new SagaTransactionException(sagaId, "deductInventory", "库存扣减失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理支付步骤
     * 
     * @param sagaId SAGA实例ID
     * @param paymentRequest 支付请求
     */
    @SagaStep(name = "processPayment", order = 3, compensationMethod = "refundPayment")
    @Transactional(rollbackFor = Exception.class)
    public void processPayment(String sagaId, Object paymentRequest) {
        try {
            log.info("处理支付: sagaId={}, paymentRequest={}", sagaId, paymentRequest);
            
            // 1. 开始步骤
            SagaStepLog stepLog = sagaManagerService.startSagaStep(
                    sagaId, 
                    "processPayment", 
                    3, 
                    JSON.toJSONString(paymentRequest), 
                    "refundPayment"
            );
            
            // 2. 执行支付业务逻辑
            String paymentResult = executePayment(paymentRequest);
            
            // 3. 完成步骤
            sagaManagerService.completeSagaStep(stepLog.getId(), paymentResult);
            
            // 4. 所有步骤完成，更新SAGA状态为完成
            sagaManagerService.updateSagaStatus(sagaId, SagaStatus.COMPLETED, "processPayment", null);
            
            log.info("支付处理完成，订单SAGA流程成功: sagaId={}", sagaId);
            
        } catch (Exception e) {
            log.error("支付处理失败: sagaId={}, error={}", sagaId, e.getMessage(), e);
            
            // 记录步骤失败
            sagaManagerService.failSagaStep(getSagaStepLogId(sagaId, "processPayment"), e.getMessage());
            
            // 开始补偿流程
            sagaManagerService.startCompensation(sagaId, "支付处理失败: " + e.getMessage());
            
            throw new SagaTransactionException(sagaId, "processPayment", "支付处理失败: " + e.getMessage(), e);
        }
    }

    // ==================== 补偿方法 ====================

    /**
     * 取消订单（补偿方法）
     * 
     * @param sagaId SAGA实例ID
     * @param compensationData 补偿数据
     */
    @SagaCompensation(forStep = "createOrder")
    public void cancelOrder(String sagaId, String compensationData) {
        try {
            log.info("执行订单取消补偿: sagaId={}, compensationData={}", sagaId, compensationData);
            
            // 执行订单取消逻辑
            executeCancelOrder(compensationData);
            
            log.info("订单取消补偿完成: sagaId={}", sagaId);
            
        } catch (Exception e) {
            log.error("订单取消补偿失败: sagaId={}, error={}", sagaId, e.getMessage(), e);
            throw new RuntimeException("订单取消补偿失败: " + e.getMessage(), e);
        }
    }

    /**
     * 恢复库存（补偿方法）
     * 
     * @param sagaId SAGA实例ID
     * @param compensationData 补偿数据
     */
    @SagaCompensation(forStep = "deductInventory")
    public void restoreInventory(String sagaId, String compensationData) {
        try {
            log.info("执行库存恢复补偿: sagaId={}, compensationData={}", sagaId, compensationData);
            
            // 执行库存恢复逻辑
            executeRestoreInventory(compensationData);
            
            log.info("库存恢复补偿完成: sagaId={}", sagaId);
            
        } catch (Exception e) {
            log.error("库存恢复补偿失败: sagaId={}, error={}", sagaId, e.getMessage(), e);
            throw new RuntimeException("库存恢复补偿失败: " + e.getMessage(), e);
        }
    }

    /**
     * 退款（补偿方法）
     * 
     * @param sagaId SAGA实例ID
     * @param compensationData 补偿数据
     */
    @SagaCompensation(forStep = "processPayment")
    public void refundPayment(String sagaId, String compensationData) {
        try {
            log.info("执行支付退款补偿: sagaId={}, compensationData={}", sagaId, compensationData);
            
            // 执行退款逻辑
            executeRefund(compensationData);
            
            log.info("支付退款补偿完成: sagaId={}", sagaId);
            
        } catch (Exception e) {
            log.error("支付退款补偿失败: sagaId={}, error={}", sagaId, e.getMessage(), e);
            throw new RuntimeException("支付退款补偿失败: " + e.getMessage(), e);
        }
    }

    // ==================== 私有方法 ====================

    private String extractOrderId(Object orderRequest) {
        // 从订单请求中提取订单ID
        return "ORDER_" + System.currentTimeMillis();
    }

    private String executeCreateOrder(Object orderRequest) {
        // 模拟订单创建逻辑
        log.info("执行订单创建业务逻辑: {}", orderRequest);
        return "ORDER_" + System.currentTimeMillis();
    }

    private String buildInventoryDeductMessage(String orderId, Object orderRequest) {
        Map<String, Object> message = new HashMap<>();
        message.put("orderId", orderId);
        message.put("orderRequest", orderRequest);
        message.put("action", "deductInventory");
        return JSON.toJSONString(message);
    }

    private String executeInventoryDeduct(Object inventoryRequest) {
        // 模拟库存扣减逻辑
        log.info("执行库存扣减业务逻辑: {}", inventoryRequest);
        return JSON.toJSONString(Map.of("deductResult", "success", "timestamp", System.currentTimeMillis()));
    }

    private String buildPaymentMessage(String sagaId, Object inventoryRequest) {
        Map<String, Object> message = new HashMap<>();
        message.put("sagaId", sagaId);
        message.put("inventoryRequest", inventoryRequest);
        message.put("action", "processPayment");
        return JSON.toJSONString(message);
    }

    private String executePayment(Object paymentRequest) {
        // 模拟支付处理逻辑
        log.info("执行支付处理业务逻辑: {}", paymentRequest);
        return JSON.toJSONString(Map.of("paymentResult", "success", "timestamp", System.currentTimeMillis()));
    }

    private void executeCancelOrder(String compensationData) {
        // 模拟订单取消逻辑
        log.info("执行订单取消逻辑: {}", compensationData);
    }

    private void executeRestoreInventory(String compensationData) {
        // 模拟库存恢复逻辑
        log.info("执行库存恢复逻辑: {}", compensationData);
    }

    private void executeRefund(String compensationData) {
        // 模拟退款逻辑
        log.info("执行退款逻辑: {}", compensationData);
    }

    private Long getSagaStepLogId(String sagaId, String stepName) {
        // 获取步骤日志ID的辅助方法
        return sagaManagerService.getSagaStepLogs(sagaId).stream()
                .filter(log -> stepName.equals(log.getStepName()))
                .map(SagaStepLog::getId)
                .findFirst()
                .orElse(null);
    }
}
