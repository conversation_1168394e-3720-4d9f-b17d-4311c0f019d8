package com.knet.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knet.common.enums.SagaStatus;
import com.knet.order.model.entity.SagaInstance;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/15 14:40
 * @description: SAGA实例 Mapper
 */
@Mapper
public interface SagaInstanceMapper extends BaseMapper<SagaInstance> {

    /**
     * 更新SAGA状态
     * 
     * @param sagaId SAGA实例ID
     * @param status 新状态
     * @param currentStep 当前步骤
     * @param failureReason 失败原因
     * @param endTime 结束时间
     * @param version 版本号（乐观锁）
     * @return 更新行数
     */
    int updateSagaStatus(@Param("sagaId") String sagaId,
                        @Param("status") SagaStatus status,
                        @Param("currentStep") String currentStep,
                        @Param("failureReason") String failureReason,
                        @Param("endTime") Date endTime,
                        @Param("version") Integer version);

    /**
     * 更新SAGA上下文数据
     * 
     * @param sagaId SAGA实例ID
     * @param contextData 上下文数据
     * @param version 版本号（乐观锁）
     * @return 更新行数
     */
    int updateSagaContext(@Param("sagaId") String sagaId,
                         @Param("contextData") String contextData,
                         @Param("version") Integer version);

    /**
     * 查询需要重试的SAGA实例
     * 
     * @param status 状态
     * @param currentTime 当前时间
     * @param limit 限制条数
     * @return SAGA实例列表
     */
    List<SagaInstance> selectRetryableSagas(@Param("status") SagaStatus status,
                                           @Param("currentTime") Date currentTime,
                                           @Param("limit") Integer limit);

    /**
     * 增加重试次数
     * 
     * @param sagaId SAGA实例ID
     * @param nextRetryTime 下次重试时间
     * @param version 版本号（乐观锁）
     * @return 更新行数
     */
    int incrementRetryCount(@Param("sagaId") String sagaId,
                           @Param("nextRetryTime") Date nextRetryTime,
                           @Param("version") Integer version);
}
