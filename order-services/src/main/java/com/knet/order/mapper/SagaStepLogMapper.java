package com.knet.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knet.common.enums.SagaStepStatus;
import com.knet.order.model.entity.SagaStepLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/15 14:45
 * @description: SAGA步骤日志 Mapper
 */
@Mapper
public interface SagaStepLogMapper extends BaseMapper<SagaStepLog> {

    /**
     * 查询SAGA的所有步骤日志
     * 
     * @param sagaId SAGA实例ID
     * @return 步骤日志列表
     */
    List<SagaStepLog> selectBySagaId(@Param("sagaId") String sagaId);

    /**
     * 查询SAGA的已完成步骤（用于补偿）
     * 
     * @param sagaId SAGA实例ID
     * @return 已完成的步骤日志列表（按执行顺序倒序）
     */
    List<SagaStepLog> selectCompletedStepsForCompensation(@Param("sagaId") String sagaId);

    /**
     * 更新步骤状态
     * 
     * @param id 步骤日志ID
     * @param status 新状态
     * @param endTime 结束时间
     * @param duration 执行耗时
     * @param outputData 输出数据
     * @param errorMessage 错误信息
     * @param version 版本号（乐观锁）
     * @return 更新行数
     */
    int updateStepStatus(@Param("id") Long id,
                        @Param("status") SagaStepStatus status,
                        @Param("endTime") Date endTime,
                        @Param("duration") Long duration,
                        @Param("outputData") String outputData,
                        @Param("errorMessage") String errorMessage,
                        @Param("version") Integer version);

    /**
     * 增加步骤重试次数
     * 
     * @param id 步骤日志ID
     * @param version 版本号（乐观锁）
     * @return 更新行数
     */
    int incrementStepRetryCount(@Param("id") Long id,
                               @Param("version") Integer version);
}
