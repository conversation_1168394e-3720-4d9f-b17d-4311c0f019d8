<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.user.mapper.SysLocalMessageMapper">

    <!-- 查询需要重试的消息 -->
    <select id="selectRetryMessages" resultType="com.knet.user.model.entity.SysLocalMessage">
        SELECT 
            id, message_id, exchange, routing_key, message_body, message_headers,
            status, retry_count, max_retry_count, next_retry_time, last_retry_time,
            failure_reason, business_type, business_id, sent_time, remark,
            create_time, update_time, del_flag, version
        FROM sys_local_message 
        WHERE del_flag = 0
          AND status = #{status}
          AND (next_retry_time IS NULL OR next_retry_time &lt;= #{currentTime})
          AND retry_count &lt; max_retry_count
        ORDER BY create_time ASC
        LIMIT #{limit}
    </select>

    <!-- 更新消息状态为发送中 -->
    <update id="updateStatusToSending">
        UPDATE sys_local_message 
        SET status = 'SENDING',
            update_time = NOW(),
            version = version + 1
        WHERE message_id = #{messageId} 
          AND version = #{version}
          AND del_flag = 0
    </update>

    <!-- 更新消息发送成功 -->
    <update id="updateStatusToSuccess">
        UPDATE sys_local_message 
        SET status = 'SUCCESS',
            sent_time = #{sentTime},
            update_time = NOW(),
            version = version + 1
        WHERE message_id = #{messageId} 
          AND version = #{version}
          AND del_flag = 0
    </update>

    <!-- 更新消息发送失败 -->
    <update id="updateStatusToFailed">
        UPDATE sys_local_message 
        SET status = #{status},
            retry_count = #{retryCount},
            next_retry_time = #{nextRetryTime},
            last_retry_time = #{lastRetryTime},
            failure_reason = #{failureReason},
            update_time = NOW(),
            version = version + 1
        WHERE message_id = #{messageId} 
          AND version = #{version}
          AND del_flag = 0
    </update>

</mapper>
