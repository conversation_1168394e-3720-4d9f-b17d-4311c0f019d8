<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.user.mapper.SysShopCartMapper">

    <!-- 查询用户购物车详情 -->
    <select id="queryCartDetails" resultType="com.knet.user.model.dto.rsp.CartQueryResult">
        SELECT cart.id           AS cartId,
               cart.user_id      AS userId,
               item.sku,
               item.product_name AS productName,
               item.image_url    AS imageUrl,
               item.selected     AS itemSelected,
               item.id           AS itemId,
               detail.id         AS detailId,
               detail.size,
               detail.quantity,
               detail.unit_price AS unitPrice,
               detail.selected   AS detailSelected
        FROM sys_shop_cart cart
                 LEFT JOIN sys_shop_cart_item item ON cart.id = item.cart_id
                 LEFT JOIN sys_shop_cart_size_detail detail ON item.id = detail.cart_item_id
        WHERE cart.user_id = #{userId}
          AND cart.del_flag = 0
          AND (item.del_flag = 0 OR item.del_flag IS NULL)
          AND (detail.del_flag = 0 OR detail.del_flag IS NULL)
        ORDER BY detail.create_time
    </select>

    <!-- 查询用户购物车中的特定商品 -->
    <select id="queryCartItem" resultType="com.knet.user.model.dto.rsp.CartQueryResult">
        SELECT cart.id           AS cartId,
               cart.user_id      AS userId,
               item.sku,
               item.product_name AS productName,
               item.image_url    AS imageUrl,
               item.selected     AS itemSelected,
               item.id           AS itemId,
               detail.id         AS detailId,
               detail.size,
               detail.quantity,
               detail.unit_price AS unitPrice,
               detail.selected   AS detailSelected
        FROM sys_shop_cart cart
                 LEFT JOIN sys_shop_cart_item item ON cart.id = item.cart_id
                 LEFT JOIN sys_shop_cart_size_detail detail ON item.id = detail.cart_item_id
        WHERE cart.user_id = #{userId}
          AND item.sku = #{sku}
          AND detail.size = #{size}
          AND cart.del_flag = 0
          AND item.del_flag = 0
          AND detail.del_flag = 0
        ORDER BY detail.update_time DESC
    </select>
</mapper>
