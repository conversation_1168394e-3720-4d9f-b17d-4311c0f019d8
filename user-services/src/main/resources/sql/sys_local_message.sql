-- 本地消息表 - 用于MQ消息发送失败时的补偿机制
CREATE TABLE `sys_local_message` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `message_id` varchar(64) NOT NULL COMMENT '消息ID，全局唯一',
  `exchange` varchar(100) NOT NULL COMMENT '交换机名称',
  `routing_key` varchar(100) NOT NULL COMMENT '路由键',
  `message_body` text NOT NULL COMMENT '消息体内容',
  `message_headers` text COMMENT '消息头信息，JSON格式',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '消息状态：PENDING-待发送，SENDING-发送中，SUCCESS-发送成功，FAILED-发送失败',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `max_retry_count` int(11) NOT NULL DEFAULT '3' COMMENT '最大重试次数',
  `next_retry_time` datetime DEFAULT NULL COMMENT '下次重试时间',
  `last_retry_time` datetime DEFAULT NULL COMMENT '最后重试时间',
  `failure_reason` varchar(500) DEFAULT NULL COMMENT '失败原因',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型，如：order、payment、user等',
  `business_id` varchar(64) DEFAULT NULL COMMENT '业务ID，关联的业务主键',
  `sent_time` datetime DEFAULT NULL COMMENT '发送成功时间',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号，用于乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_message_id` (`message_id`),
  KEY `idx_status_retry_time` (`status`, `next_retry_time`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='本地消息表-MQ消息发送失败补偿机制';

-- 创建索引说明：
-- 1. uk_message_id: 消息ID唯一索引，保证消息不重复
-- 2. idx_status_retry_time: 状态和重试时间复合索引，用于定时任务查询待重试消息
-- 3. idx_business_type: 业务类型索引，用于按业务类型查询
-- 4. idx_business_id: 业务ID索引，用于按业务ID查询
-- 5. idx_create_time: 创建时间索引，用于按时间排序查询
-- 6. idx_del_flag: 删除标志索引，用于逻辑删除过滤
