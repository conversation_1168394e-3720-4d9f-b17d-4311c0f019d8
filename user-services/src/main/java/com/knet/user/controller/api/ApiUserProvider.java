package com.knet.user.controller.api;

import com.knet.common.base.HttpResult;
import com.knet.common.enums.OperationResult;
import com.knet.common.enums.UserOperationType;
import com.knet.user.service.ISysUserOperationRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/27 16:39
 * @description: 用户服务-对外提供服务
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = "用户服务-对外提供接口", description = "用户服务-对外提供接口")
public class ApiUserProvider {
    @Resource
    private ISysUserOperationRecordService sysUserOperationRecordService;

    /**
     * 创建用户操作记录（简化版本，供其他服务调用）
     *
     * @param userId          用户ID
     * @param operatorId      操作者ID
     * @param operatorType    操作者类型
     * @param operationType   操作类型
     * @param operationResult 操作结果
     * @param operationDesc   操作描述
     * @param businessId      关联业务ID
     * @param businessType    关联业务类型
     * @return 操作记录ID
     */
    @Operation(summary = "创建用户操作记录", description = "供其他服务调用的简化版本")
    @PostMapping("/create-simple")
    public HttpResult<String> createSimple(
            @RequestParam("userId") Long userId,
            @RequestParam("operatorId") Long operatorId,
            @RequestParam("operatorType") String operatorType,
            @RequestParam("operationType") UserOperationType operationType,
            @RequestParam("operationResult") OperationResult operationResult,
            @RequestParam("operationDesc") String operationDesc,
            @RequestParam(value = "businessId", required = false) String businessId,
            @RequestParam(value = "businessType", required = false) String businessType) {
        log.info("创建用户操作记录（简化版）: userId={}, operationType={}, operationResult={}", userId, operationType, operationResult);
        String operationId = sysUserOperationRecordService.createOperationRecord(
                userId, operatorId, operatorType, operationType, operationResult,
                operationDesc, businessId, businessType);
        return HttpResult.ok(operationId);
    }
}
