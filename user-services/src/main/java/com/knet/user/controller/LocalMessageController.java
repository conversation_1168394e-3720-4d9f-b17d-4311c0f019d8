package com.knet.user.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.knet.common.annotation.Loggable;
import com.knet.common.base.HttpResult;
import com.knet.user.model.dto.req.SaveLocalMessageRequest;
import com.knet.user.model.entity.SysLocalMessage;
import com.knet.user.service.ISysLocalMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/15 11:10
 * @description: 本地消息表控制器 - 为其他服务提供消息保存支持
 */
@Slf4j
@RestController
@RequestMapping("/localMessage")
@Tag(name = "本地消息表管理", description = "提供MQ消息发送失败时的补偿机制")
public class LocalMessageController {

    @Resource
    private ISysLocalMessageService sysLocalMessageService;

    /**
     * 保存本地消息
     * 
     * @param request 保存消息请求
     * @return 保存结果
     */
    @Loggable(value = "保存本地消息")
    @Operation(summary = "保存本地消息", description = "当MQ消息发送失败时，保存到本地消息表")
    @PostMapping("/save")
    public HttpResult<SysLocalMessage> saveLocalMessage(@Validated @RequestBody SaveLocalMessageRequest request) {
        log.info("接收到保存本地消息请求: messageId={}, businessType={}", 
                request.getMessageId(), request.getBusinessType());
        
        SysLocalMessage savedMessage = sysLocalMessageService.saveLocalMessage(request);
        
        log.info("本地消息保存成功: messageId={}", savedMessage.getMessageId());
        return HttpResult.ok(savedMessage);
    }

    /**
     * 保存消息并立即尝试发送
     * 
     * @param request 保存消息请求
     * @return 发送结果
     */
    @Loggable(value = "保存并发送本地消息")
    @Operation(summary = "保存并发送消息", description = "保存消息到本地表并立即尝试发送")
    @PostMapping("/saveAndSend")
    public HttpResult<Boolean> saveAndSendMessage(@Validated @RequestBody SaveLocalMessageRequest request) {
        log.info("接收到保存并发送消息请求: messageId={}, businessType={}", 
                request.getMessageId(), request.getBusinessType());
        
        boolean sendResult = sysLocalMessageService.saveAndSendMessage(request);
        
        log.info("保存并发送消息完成: messageId={}, 发送结果={}", request.getMessageId(), sendResult);
        return HttpResult.ok(sendResult);
    }

    /**
     * 重新发送指定消息
     * 
     * @param messageId 消息ID
     * @return 发送结果
     */
    @Loggable(value = "重新发送消息")
    @Operation(summary = "重新发送消息", description = "重新发送指定的消息")
    @PostMapping("/resend/{messageId}")
    public HttpResult<Boolean> resendMessage(
            @Parameter(description = "消息ID") @PathVariable String messageId) {
        log.info("接收到重新发送消息请求: messageId={}", messageId);
        
        boolean sendResult = sysLocalMessageService.sendMessage(messageId);
        
        log.info("重新发送消息完成: messageId={}, 发送结果={}", messageId, sendResult);
        return HttpResult.ok(sendResult);
    }

    /**
     * 批量重新发送消息
     * 
     * @param messageIds 消息ID列表
     * @return 发送成功的消息数量
     */
    @Loggable(value = "批量重新发送消息")
    @Operation(summary = "批量重新发送消息", description = "批量重新发送指定的消息")
    @PostMapping("/batchResend")
    public HttpResult<Integer> batchResendMessages(@RequestBody List<String> messageIds) {
        log.info("接收到批量重新发送消息请求: 消息数量={}", messageIds.size());
        
        int successCount = sysLocalMessageService.batchSendMessages(messageIds);
        
        log.info("批量重新发送消息完成: 总数={}, 成功={}", messageIds.size(), successCount);
        return HttpResult.ok(successCount);
    }

    /**
     * 手动处理重试消息
     * 
     * @param limit 处理数量限制
     * @return 处理的消息数量
     */
    @Loggable(value = "手动处理重试消息")
    @Operation(summary = "手动处理重试消息", description = "手动触发重试消息处理")
    @PostMapping("/processRetry")
    public HttpResult<Integer> processRetryMessages(
            @Parameter(description = "处理数量限制，默认100") @RequestParam(defaultValue = "100") Integer limit) {
        log.info("接收到手动处理重试消息请求: limit={}", limit);
        
        int processedCount = sysLocalMessageService.processRetryMessages(limit);
        
        log.info("手动处理重试消息完成: 处理数量={}", processedCount);
        return HttpResult.ok(processedCount);
    }

    /**
     * 查询消息详情
     * 
     * @param messageId 消息ID
     * @return 消息详情
     */
    @Operation(summary = "查询消息详情", description = "根据消息ID查询消息详情")
    @GetMapping("/{messageId}")
    public HttpResult<SysLocalMessage> getMessageById(
            @Parameter(description = "消息ID") @PathVariable String messageId) {
        log.info("查询消息详情: messageId={}", messageId);
        
        SysLocalMessage message = sysLocalMessageService.lambdaQuery()
                .eq(SysLocalMessage::getMessageId, messageId)
                .one();
        
        return HttpResult.ok(message);
    }

    /**
     * 分页查询消息列表
     * 
     * @param current 当前页
     * @param size 页大小
     * @param businessType 业务类型
     * @param status 消息状态
     * @return 消息列表
     */
    @Operation(summary = "分页查询消息列表", description = "分页查询本地消息列表")
    @GetMapping("/page")
    public HttpResult<IPage<SysLocalMessage>> getMessagePage(
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") Long current,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") Long size,
            @Parameter(description = "业务类型") @RequestParam(required = false) String businessType,
            @Parameter(description = "消息状态") @RequestParam(required = false) String status) {
        
        log.info("分页查询消息列表: current={}, size={}, businessType={}, status={}", 
                current, size, businessType, status);
        
        Page<SysLocalMessage> page = new Page<>(current, size);
        IPage<SysLocalMessage> result = sysLocalMessageService.lambdaQuery()
                .eq(businessType != null, SysLocalMessage::getBusinessType, businessType)
                .eq(status != null, SysLocalMessage::getStatus, status)
                .orderByDesc(SysLocalMessage::getCreateTime)
                .page(page);
        
        return HttpResult.ok(result);
    }

    /**
     * 获取需要重试的消息列表
     * 
     * @param limit 限制数量
     * @return 需要重试的消息列表
     */
    @Operation(summary = "获取需要重试的消息", description = "获取当前需要重试的消息列表")
    @GetMapping("/retry")
    public HttpResult<List<SysLocalMessage>> getRetryMessages(
            @Parameter(description = "限制数量，默认100") @RequestParam(defaultValue = "100") Integer limit) {
        
        log.info("获取需要重试的消息: limit={}", limit);
        
        List<SysLocalMessage> retryMessages = sysLocalMessageService.getRetryMessages(limit);
        
        log.info("获取需要重试的消息完成: 数量={}", retryMessages.size());
        return HttpResult.ok(retryMessages);
    }
}
