package com.knet.user.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/21 14:50
 * @description: 更新购物车请求
 */
@Data
@Schema(description = "更新购物车请求")
@EqualsAndHashCode(callSuper = true)
public class UpdateCartRequest extends BaseRequest {

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long userId;

    @NotNull(message = "购物车ID")
    @Schema(description = "购物车ID")
    private Long cartId;

    @NotNull(message = "购物车商品项ID")
    @Schema(description = "购物车商品项ID")
    private Long cartItemId;

    @NotBlank(message = "商品SKU不能为空")
    @Schema(description = "商品SKU", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sku;

    @NotBlank(message = "尺码不能为空")
    @Schema(description = "尺码值", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
    private String size;

    @NotEmpty(message = "更新购物车商品明细列表-不能为空")
    @Schema(description = "更新购物车商品明细列表")
    private List<UpdateCartItemVo> vos;

    @Schema(description = "更新购物车请求内部VO")
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class UpdateCartItemVo extends BaseRequest {
        @NotNull(message = "购物车商品项ID")
        @Schema(description = "购物车商品项ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Long itemId;

        @NotNull(message = "购物车商品明细ID")
        @Schema(description = "购物车商品明细ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
        private Long detailId;

        @NotNull(message = "用户ID不能为空")
        @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
        private Long userId;

        @Min(value = 1, message = "数量必须大于0")
        @Schema(description = "数量", example = "2")
        private Integer quantity;

        @Schema(description = "是否选中 (0-未选中, 1-已选中) 默认选中", example = "1")
        private Integer selected = 1;
    }
}
