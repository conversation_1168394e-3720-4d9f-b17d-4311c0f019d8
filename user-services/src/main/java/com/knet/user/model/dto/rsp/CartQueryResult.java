package com.knet.user.model.dto.rsp;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/21 15:20
 * @description: 购物车查询结果DTO，用于接收SQL查询结果
 */
@Data
public class CartQueryResult {

    /**
     * 购物车ID
     */
    private Long cartId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商品SKU
     */
    private String sku;
    
    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品图片URL
     */
    private String imageUrl;

    /**
     * 商品项是否选中 (0-未选中, 1-已选中)
     */
    private Integer itemSelected;

    /**
     * 商品项ID
     */
    private Long itemId;

    /**
     * 尺码明细ID
     */
    private Long detailId;

    /**
     * 尺码值
     */
    private String size;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 单价
     */
    private String unitPrice;

    /**
     * 尺码明细是否选中 (0-未选中, 1-已选中)
     */
    private Integer detailSelected;
}
