package com.knet.user.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/21 14:45
 * @description: 添加商品到购物车请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "添加商品到购物车请求")
public class AddToCartRequest extends BaseRequest {

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long userId;

    @NotBlank(message = "商品名称不能为空")
    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productName;

    @NotBlank(message = "商品SKU不能为空")
    @Schema(description = "商品SKU", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sku;

    @Schema(description = "商品图片URL")
    private String imageUrl;

    @NotEmpty(message = "尺码明细不能为空")
    @Valid
    @Schema(description = "尺码明细列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<SizeDetailRequest> sizeDetails;

    /**
     * 尺码明细请求
     */
    @Data
    @Schema(description = "尺码明细请求")
    public static class SizeDetailRequest {

        @NotBlank(message = "尺码不能为空")
        @Schema(description = "尺码值", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
        private String size;

        @NotNull(message = "数量不能为空")
        @Min(value = 1, message = "数量必须大于0")
        @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Integer quantity;

        @NotNull(message = "单价不能为空")
        @Schema(description = "单价（单位：美分）", requiredMode = Schema.RequiredMode.REQUIRED, example = "9999")
        private String unitPrice;
    }
}
