package com.knet.user.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.user.model.enums.MessageStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/15 10:00
 * @description: 本地消息表 - 用于MQ消息发送失败时的补偿机制
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_local_message", description = "本地消息表")
@TableName("sys_local_message")
public class SysLocalMessage extends BaseEntity {

    @Schema(description = "消息ID，全局唯一", requiredMode = Schema.RequiredMode.REQUIRED)
    private String messageId;

    @Schema(description = "交换机名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String exchange;

    @Schema(description = "路由键", requiredMode = Schema.RequiredMode.REQUIRED)
    private String routingKey;

    @Schema(description = "消息体内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String messageBody;

    @Schema(description = "消息头信息，JSON格式")
    private String messageHeaders;

    @Schema(description = "消息状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private MessageStatus status;

    @Schema(description = "重试次数，默认0")
    @Builder.Default
    private Integer retryCount = 0;

    @Schema(description = "最大重试次数，默认3")
    @Builder.Default
    private Integer maxRetryCount = 3;

    @Schema(description = "下次重试时间")
    private Date nextRetryTime;

    @Schema(description = "最后重试时间")
    private Date lastRetryTime;

    @Schema(description = "失败原因")
    private String failureReason;

    @Schema(description = "业务类型，如：order、payment、user等")
    private String businessType;

    @Schema(description = "业务ID，关联的业务主键")
    private String businessId;

    @Schema(description = "发送成功时间")
    private Date sentTime;

    @Schema(description = "备注信息")
    private String remark;
}
