package com.knet.user.model.dto.rsp;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/21 15:10
 * @description: 尺码明细响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "尺码明细响应聚合信息")
public class SizeDetailSumResponse {

    @Schema(description = "购物车商品项ID")
    private Long cartItemId;

    @Schema(description = "尺码值", example = "5")
    private String size;

    @Schema(description = "数量", example = "1")
    private Integer quantity;

    @Schema(description = "尺码平均价格，美元", example = "99.99")
    private String avgPrice;

    @Schema(description = "尺码总金额，美元", example = "99.99")
    private String totalPrice;
}
