package com.knet.user.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2025/2/14 17:56
 * @description: 用户地址请求体
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserAddressSaveRequest extends BaseRequest {

    @Schema(description = "userId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "userId is mandatory")
    private Long userId;

    @Schema(description = "fullName", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotBlank(message = "Full name is mandatory")
    @Size(max = 100, message = "Full name must be less than 100 characters")
    private String fullName;

    @Schema(description = "idNumber", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Size(max = 18, message = "ID number must be 18 characters")
    private String idNumber;

    @Schema(description = "companyName", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Size(max = 100, message = "Company name must be less than 100 characters")
    private String companyName;

    @Schema(description = "country", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotBlank(message = "Country is mandatory")
    @Size(max = 50, message = "Country must be less than 50 characters")
    private String country;

    @Schema(description = "addressLine1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotBlank(message = "Address line 1 is mandatory")
    @Size(max = 255, message = "Address line 1 must be less than 255 characters")
    private String addressLine1;

    @Schema(description = "addressLine2", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Size(max = 255, message = "Address line 2 must be less than 255 characters")
    private String addressLine2;

    @Schema(description = "city", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotBlank(message = "City is mandatory")
    @Size(max = 50, message = "City must be less than 50 characters")
    private String city;

    @Schema(description = "state", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotBlank(message = "State/Province is mandatory")
    @Size(max = 50, message = "State/Province must be less than 50 characters")
    private String state;

    @Schema(description = "zipCode", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotBlank(message = "Zip code is mandatory")
    @Size(max = 20, message = "Zip code must be less than 20 characters")
    private String zipCode;

    @NotBlank(message = "phoneNumber is mandatory")
    @Schema(description = "phoneNumber", requiredMode = Schema.RequiredMode.REQUIRED)
    @Pattern(regexp = "^\\d+$", message = "Phone number must contain only digits")
    @Size(max = 20, message = "Phone number must be less than 20 characters")
    private String phoneNumber;

    @NotBlank(message = "ZphonePrefix is mandatory")
    @Schema(description = "phonePrefix", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @Pattern(regexp = "^\\d+$", message = "Phone prefix format is incorrect")
    private String phonePrefix;
}
