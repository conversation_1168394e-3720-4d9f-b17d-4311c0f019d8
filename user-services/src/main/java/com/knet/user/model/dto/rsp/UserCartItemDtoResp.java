package com.knet.user.model.dto.rsp;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/21 10:35
 * @description: 用户购物车项响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户购物车项响应")
public class UserCartItemDtoResp {

    @Schema(description = "购物车项ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "商品SKU")
    private String sku;

    @Schema(description = "商品规格/尺码")
    private String spec;

    @Schema(description = "商品数量", example = "1")
    private Integer quantity;

    @Schema(description = "商品单价（美元）", example = "10.75")
    private String price;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品图片URL")
    private String imageUrl;

    @Schema(description = "是否选中 (0-未选中, 1-已选中)", example = "1")
    private Integer selected;

    @Schema(description = "加入购物车时间")
    private Date addTime;

    @Schema(description = "最后更新时间")
    private Date updateCartTime;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
