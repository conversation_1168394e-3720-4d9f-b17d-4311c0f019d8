package com.knet.user.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/15 10:05
 * @description: 消息状态枚举
 */
@Getter
@AllArgsConstructor
public enum MessageStatus {
    
    PENDING(0, "待发送"),
    SENDING(1, "发送中"),
    SUCCESS(2, "发送成功"),
    FAILED(3, "发送失败");

    private final Integer code;
    
    @EnumValue
    @JsonValue
    private final String name;
}
