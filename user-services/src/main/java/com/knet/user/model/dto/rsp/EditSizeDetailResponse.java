package com.knet.user.model.dto.rsp;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/21 15:10
 * @description: 尺码明细响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "尺码明细响应")
public class EditSizeDetailResponse {

    @Schema(description = "尺码明细列表")
    private List<EditCartItemResponse> details;

    @Schema(description = "数量", example = "1")
    private Integer totalQuantity;

    @Schema(description = "平均价格，美元", example = "9999")
    private String avgPrice;

    @Schema(description = "总金额，美元", example = "9999")
    private String totalPrice;
}
