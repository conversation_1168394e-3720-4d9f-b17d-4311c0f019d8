package com.knet.user.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.UserOperationRecordMessage;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.user.model.dto.req.CreateUserOperationRecordRequest;
import com.knet.user.service.ISysUserOperationRecordService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/5/20 20:30
 * @description: 用户操作记录消费者
 */
@Slf4j
@Component
public class UserOperationRecordConsumer {
    @Resource
    private ISysUserOperationRecordService sysUserOperationRecordService;
    @Resource
    private RedisCacheUtil redisCacheUtil;

    /**
     * 消费用户操作记录消息
     *
     * @param messageBody 消息内容
     * @param routingKey  路由键
     * @param messageId   消息ID
     * @param channel     通道
     * @param deliveryTag 投递标签
     */
    @RabbitListener(
            queues = "user-operation-queue.user-services",
            ackMode = "MANUAL"
    )
    public void consumeUserOperationRecord(
            @Payload String messageBody,
            @Header("routingKey") String routingKey,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        log.info("接收到用户操作记录消息: messageId={}, routingKey={}", messageId, routingKey);
        try {
            // 幂等性检查
            if (!redisCacheUtil.setIfAbsent(messageId, "PROCESSED", 30)) {
                log.warn("重复消息，已处理过: messageId={}", messageId);
                channel.basicAck(deliveryTag, false);
                return;
            }
            // 根据routingKey进行不同的处理
            if (routingKey.startsWith("user.operation.")) {
                processUserOperation(messageBody, routingKey, messageId);
                channel.basicAck(deliveryTag, false);
            } else {
                log.warn("未知的routingKey: {}, messageId: {}", routingKey, messageId);
                channel.basicAck(deliveryTag, false);
            }
        } catch (Exception e) {
            log.error("用户操作记录处理失败: messageId={},  error={}", messageId, e.getMessage(), e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ioException) {
                log.error("消息确认失败: messageId={}, error={}", messageId, ioException.getMessage());
            }
        }
    }

    /**
     * 处理用户操作相关消息
     *
     * @param messageBody 消息体
     * @param routingKey  路由键
     * @param messageId   消息ID
     */
    private void processUserOperation(String messageBody, String routingKey, String messageId) {
        log.info("处理用户操作消息: messageId={}, routingKey={}", messageId, routingKey);
        // 根据routingKey的具体类型进行处理
        switch (routingKey) {
            case "user.operation.record":
                processUserOperationRecord(messageBody, messageId);
                break;
            // 将来可以扩展其他类型
            // case "user.operation.notification":
            //     processUserOperationNotification(messageBody, messageId);
            //     break;
            default:
                log.warn("未支持的user.operation类型: {}, messageId: {}", routingKey, messageId);
                break;
        }
    }

    /**
     * 处理用户操作记录
     *
     * @param messageBody 消息体
     * @param messageId   消息ID
     */
    private void processUserOperationRecord(String messageBody, String messageId) {
        log.info("处理用户操作记录: messageId={}", messageId);
        UserOperationRecordMessage recordMessage = JSON.parseObject(messageBody, UserOperationRecordMessage.class);
        createOperationRecord(recordMessage, messageId);
    }

    /**
     * 创建操作记录
     */
    private void createOperationRecord(UserOperationRecordMessage recordMessage, String messageId) {
        CreateUserOperationRecordRequest request = CreateUserOperationRecordRequest.create(recordMessage);
        String operationId = sysUserOperationRecordService.createOperationRecord(request);
        log.info("用户操作记录创建成功: messageId={}, operationId={}, userId={}", messageId, operationId, recordMessage.getUserId());
    }
}
