package com.knet.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knet.user.model.entity.SysLocalMessage;
import com.knet.user.model.enums.MessageStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/15 10:10
 * @description: 本地消息表 Mapper
 */
@Mapper
public interface SysLocalMessageMapper extends BaseMapper<SysLocalMessage> {

    /**
     * 查询需要重试的消息
     * 
     * @param status 消息状态
     * @param currentTime 当前时间
     * @param limit 限制条数
     * @return 需要重试的消息列表
     */
    List<SysLocalMessage> selectRetryMessages(@Param("status") MessageStatus status, 
                                             @Param("currentTime") Date currentTime, 
                                             @Param("limit") Integer limit);

    /**
     * 更新消息状态为发送中
     * 
     * @param messageId 消息ID
     * @param version 版本号（乐观锁）
     * @return 更新行数
     */
    int updateStatusToSending(@Param("messageId") String messageId, 
                             @Param("version") Integer version);

    /**
     * 更新消息发送成功
     * 
     * @param messageId 消息ID
     * @param sentTime 发送成功时间
     * @param version 版本号（乐观锁）
     * @return 更新行数
     */
    int updateStatusToSuccess(@Param("messageId") String messageId, 
                             @Param("sentTime") Date sentTime,
                             @Param("version") Integer version);

    /**
     * 更新消息发送失败
     * 
     * @param messageId 消息ID
     * @param retryCount 重试次数
     * @param nextRetryTime 下次重试时间
     * @param lastRetryTime 最后重试时间
     * @param failureReason 失败原因
     * @param status 消息状态
     * @param version 版本号（乐观锁）
     * @return 更新行数
     */
    int updateStatusToFailed(@Param("messageId") String messageId,
                            @Param("retryCount") Integer retryCount,
                            @Param("nextRetryTime") Date nextRetryTime,
                            @Param("lastRetryTime") Date lastRetryTime,
                            @Param("failureReason") String failureReason,
                            @Param("status") MessageStatus status,
                            @Param("version") Integer version);
}
