package com.knet.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.user.model.dto.req.AddToCartRequest;
import com.knet.user.model.entity.SysShopCartSizeDetail;

/**
 * <AUTHOR>
 * @description 针对表【sys_shop_cart_size_detail(购物车尺码明细表)】的数据库操作Service
 * @date 2025-05-21 10:14:37
 */
public interface ISysShopCartSizeDetailService extends IService<SysShopCartSizeDetail> {

    /**
     * 获取或购物车尺码明细记录
     *
     * @param sizeDetail 添加购物车请求中的尺码明细
     * @param cartItemId 购物车商品项id
     * @return 购物车尺码明细记录
     */
    SysShopCartSizeDetail getSysShopCartSizeDetail(AddToCartRequest.SizeDetailRequest sizeDetail, Long cartItemId);
}
