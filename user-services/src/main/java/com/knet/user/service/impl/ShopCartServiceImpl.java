package com.knet.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.user.model.dto.req.AddToCartRequest;
import com.knet.user.model.dto.req.EditCartItemRequest;
import com.knet.user.model.dto.req.RemoveFromCartRequest;
import com.knet.user.model.dto.req.UpdateCartRequest;
import com.knet.user.model.dto.rsp.*;
import com.knet.user.model.entity.SysShopCart;
import com.knet.user.model.entity.SysShopCartItem;
import com.knet.user.model.entity.SysShopCartSizeDetail;
import com.knet.user.service.IShopCartService;
import com.knet.user.service.ISysShopCartItemService;
import com.knet.user.service.ISysShopCartService;
import com.knet.user.service.ISysShopCartSizeDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.knet.common.constants.UserServicesConstants.*;

/**
 * <AUTHOR>
 * @date 2025/5/21 11:49
 * @description: 购物车服务实现
 */
@Slf4j
@Service
public class ShopCartServiceImpl implements IShopCartService {
    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private ISysShopCartService iSysShopCartService;
    @Resource
    private ISysShopCartItemService iSysShopCartItemService;
    @Resource
    private ISysShopCartSizeDetailService iSysShopCartSizeDetailService;

    @DistributedLock(key = "'addToCart:' + #request.hashCode()", expire = 1)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CartResponse addToCart(AddToCartRequest request) {
        log.info("添加商品到购物车, request: {}", request);
        try {
            // 1. 获取或创建购物车
            SysShopCart cart = iSysShopCartService.getOrCreateCart(request.getUserId());
            // 2. 获取或创建购物车商品项
            SysShopCartItem cartItem = iSysShopCartItemService.getOrCreateShopCartItem(request, cart);
            // 3. 处理尺码明细记录
            for (AddToCartRequest.SizeDetailRequest sizeDetail : request.getSizeDetails()) {
                SysShopCartSizeDetail existingSizeDetail = iSysShopCartSizeDetailService.getSysShopCartSizeDetail(sizeDetail, cartItem.getId());
                if (BeanUtil.isNotEmpty(existingSizeDetail)) {
                    LambdaUpdateWrapper<SysShopCartSizeDetail> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper
                            .eq(SysShopCartSizeDetail::getId, existingSizeDetail.getId())
                            .set(SysShopCartSizeDetail::getQuantity, existingSizeDetail.getQuantity());
                    iSysShopCartSizeDetailService.update(null, updateWrapper);
                } else {
                    // 如果不存在尺码明细记录，则创建新的尺码明细记录
                    SysShopCartSizeDetail build = SysShopCartSizeDetail.builder()
                            .cartItemId(cartItem.getId())
                            .size(sizeDetail.getSize())
                            .quantity(sizeDetail.getQuantity())
                            .unitPrice(Long.valueOf(sizeDetail.getUnitPrice()))
                            .selected(1)
                            .build();
                    iSysShopCartSizeDetailService.save(build);
                }
            }
        } catch (Exception e) {
            log.error("添加商品到购物车失败, request: {}, 错误: {}", request, e.getMessage(), e);
            throw new ServiceException("添加商品到购物车失败,稍后再试");
        }
        // 清除购物车缓存
        String cacheKey = String.format(USER_SHOP_CART_CACHE_KEY_PREFIX, request.getUserId());
        redisCacheUtil.del(cacheKey);
        // 4. 返回更新后的购物车信息
        return getCart(request.getUserId());
    }

    @DistributedLock(key = "'updateCart:' + #request.hashCode()", expire = 1)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCart(UpdateCartRequest request) {
        log.info("更新购物车商品, request: {}", request);
        // 用户只能更新处理自己的购物车商品
        SysShopCart shopCart = iSysShopCartService.getById(request.getCartId());
        if (BeanUtil.isEmpty(shopCart) || !shopCart.getUserId().equals(request.getUserId())) {
            throw new ServiceException("无权操作其他人的购物车");
        }
        SysShopCartItem sysShopCartItem = iSysShopCartItemService.getById(request.getCartItemId());
        if (BeanUtil.isEmpty(sysShopCartItem) || !sysShopCartItem.getCartId().equals(request.getCartId())) {
            throw new ServiceException("无权操作其他人的购物车");
        }
        for (UpdateCartRequest.UpdateCartItemVo vo : request.getVos()) {
            // 当数量为0时，删除该尺码明细记录
            if (vo.getQuantity() == 0) {
                LambdaQueryWrapper<SysShopCartSizeDetail> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper
                        .eq(SysShopCartSizeDetail::getId, vo.getDetailId())
                        .eq(SysShopCartSizeDetail::getCartItemId, sysShopCartItem.getId());
                iSysShopCartSizeDetailService.remove(queryWrapper);
            } else {
                LambdaUpdateWrapper<SysShopCartSizeDetail> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper
                        .eq(SysShopCartSizeDetail::getId, vo.getDetailId())
                        .eq(SysShopCartSizeDetail::getCartItemId, sysShopCartItem.getId())
                        .set(SysShopCartSizeDetail::getQuantity, vo.getQuantity());
                iSysShopCartSizeDetailService.update(null, updateWrapper);
            }
        }
        String shopCartCacheKey = String.format(USER_SHOP_CART_CACHE_KEY_PREFIX, request.getUserId());
        redisCacheUtil.del(shopCartCacheKey);
        String shopCartItemCacheKey = String.format(USER_SHOP_CART_ITEM_CACHE_KEY_PREFIX, request.getUserId(), request.getSku(), request.getSize());
        redisCacheUtil.del(shopCartItemCacheKey);
    }

    @DistributedLock(key = "'removeFromCart:' + #request.hashCode()", expire = 1)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeFromCart(RemoveFromCartRequest request) {
        log.info("从购物车移除商品, request: {}", request);
        LambdaQueryWrapper<SysShopCart> cartQueryWrapper = new LambdaQueryWrapper<>();
        cartQueryWrapper.eq(SysShopCart::getUserId, request.getUserId());
        SysShopCart cart = iSysShopCartService.getOne(cartQueryWrapper);
        if (BeanUtil.isEmpty(cart)) {
            throw new ServiceException("购物车不存在");
        }
        // 2. 清空购物车
        if (Boolean.TRUE.equals(request.getClearAll())) {
            iSysShopCartService.deleteCart(request.getUserId());
        }
        //3. 从购物车移除指定商品
        if (Boolean.FALSE.equals(request.getClearAll())) {
            if (BeanUtil.isNotEmpty(request.getCartItemId())) {
                LambdaQueryWrapper<SysShopCartItem> itemQueryWrapper = new LambdaQueryWrapper<>();
                itemQueryWrapper
                        .eq(SysShopCartItem::getCartId, cart.getId())
                        .eq(SysShopCartItem::getId, request.getCartItemId());
                SysShopCartItem cartItem = iSysShopCartItemService.getOne(itemQueryWrapper);
                //操作商品是否存在
                if (BeanUtil.isNotEmpty(cartItem)) {
                    if (StrUtil.isBlank(request.getSize())) {
                        //删除 cartItem cartDetail
                        LambdaQueryWrapper<SysShopCartSizeDetail> sizeDetailQueryWrapper = new LambdaQueryWrapper<>();
                        sizeDetailQueryWrapper
                                .eq(SysShopCartSizeDetail::getCartItemId, cartItem.getId());
                        iSysShopCartSizeDetailService.remove(sizeDetailQueryWrapper);
                        iSysShopCartItemService.removeById(cartItem.getId());
                    } else {
                        LambdaQueryWrapper<SysShopCartSizeDetail> sizeDetailQueryWrapper = new LambdaQueryWrapper<>();
                        sizeDetailQueryWrapper
                                .eq(BeanUtil.isNotEmpty(request.getCartItemId()), SysShopCartSizeDetail::getCartItemId, cartItem.getId())
                                .eq(StrUtil.isNotEmpty(request.getSize()), SysShopCartSizeDetail::getSize, request.getSize());
                        iSysShopCartSizeDetailService.remove(sizeDetailQueryWrapper);
                    }
                }
                iSysShopCartItemService.remove(itemQueryWrapper);
                String cacheKey = String.format(USER_SHOP_CART_CACHE_KEY_PREFIX, request.getUserId());
                redisCacheUtil.del(cacheKey);
            }
        }
    }

    @Override
    public CartResponse getCart(Long userId) {
        log.info("获取购物车信息, userId: {}", userId);
        String cacheKey = String.format(USER_SHOP_CART_CACHE_KEY_PREFIX, userId);
        Object cachedCart = redisCacheUtil.get(cacheKey);
        if (cachedCart != null) {
            log.info("从缓存中获取购物车信息, userId: {}", userId);
            return (CartResponse) cachedCart;
        }
        // 缓存中没有，使用SQL联表查询获取购物车详情
        log.info("从数据库中获取购物车信息, userId: {}", userId);
        List<CartQueryResult> cartDetails = iSysShopCartService.queryCartDetails(userId);
        // 如果查询结果为空，返回空购物车
        if (CollUtil.isEmpty(cartDetails)) {
            CartResponse emptyCart = CartResponse.init(userId);
            // 缓存空购物车，设置较短的过期时间（5分钟）
            redisCacheUtil.set(cacheKey, emptyCart, 300);
            return emptyCart;
        }
        // 单价分转美元
        cartDetails.forEach(detail -> {
            if (detail.getUnitPrice() == null) {
                detail.setUnitPrice("0.00");
            } else {
                detail.setUnitPrice(PriceFormatUtil.formatCentsToYuan(detail.getUnitPrice()));
            }
        });
        Long cartId = cartDetails.get(0).getCartId();
        // 按商品项ID分组
        Map<Long, List<CartQueryResult>> itemMap = cartDetails
                .stream()
                .collect(Collectors.groupingBy(CartQueryResult::getItemId));
        // 构建购物车商品项响应
        List<CartItemResponse> itemResponses = new ArrayList<>();
        int totalQuantity = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;
        // 遍历每个商品项
        for (Map.Entry<Long, List<CartQueryResult>> entry : itemMap.entrySet()) {
            Long itemId = entry.getKey();
            List<CartQueryResult> itemDetails = entry.getValue();
            // 如果商品项为空，跳过
            if (CollUtil.isEmpty(itemDetails)) {
                continue;
            }
            // 获取商品项基本信息（从第一条记录中获取）
            CartQueryResult firstDetail = itemDetails.get(0);
            // 构建尺码明细响应
            List<SizeDetailResponse> sizeDetailResponses = new ArrayList<>();
            int itemTotalQuantity = 0;
            BigDecimal itemTotalAmount = BigDecimal.ZERO;
            // 遍历每个尺码明细
            for (CartQueryResult detail : itemDetails) {
                // 如果尺码为空，跳过（可能是LEFT JOIN导致的NULL记录）
                if (detail.getSize() == null) {
                    continue;
                }
                double subtotal = Double.parseDouble(detail.getUnitPrice()) * Long.valueOf(detail.getQuantity());
                itemTotalQuantity += detail.getQuantity();
                itemTotalAmount = itemTotalAmount.add(BigDecimal.valueOf(subtotal));
                SizeDetailResponse sizeDetailResponse = SizeDetailResponse.createSizeDetailResponse(detail, itemId);
                sizeDetailResponses.add(sizeDetailResponse);
            }
            // 构建尺码明细聚合信息
            List<SizeDetailSumResponse> sizeDetailSums = buildSizeDetailSums(itemDetails);
            // 构建商品项响应
            CartItemResponse itemResponse = CartItemResponse
                    .builder()
                    .itemId(itemId)
                    .cartId(cartId)
                    .productName(firstDetail.getProductName())
                    .sku(firstDetail.getSku())
                    .imageUrl(firstDetail.getImageUrl())
                    .sizeDetails(sizeDetailResponses)
                    .sizeDetailSums(sizeDetailSums)
                    .totalQuantity(itemTotalQuantity)
                    .totalAmount(PriceFormatUtil.formatPrice(itemTotalAmount))
                    .build();
            itemResponses.add(itemResponse);
            totalQuantity += itemTotalQuantity;
            totalAmount = totalAmount.add(itemTotalAmount);
        }
        // 计算平均金额并格式化为两位小数
        BigDecimal avgPrice = totalQuantity > 0 ? totalAmount.divide(new BigDecimal(totalQuantity), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
        CartResponse cartResponse = CartResponse.builder()
                .cartId(cartId)
                .userId(userId)
                .items(itemResponses)
                .totalQuantity(totalQuantity)
                .totalAmount(PriceFormatUtil.formatPrice(totalAmount))
                .avgAmount(PriceFormatUtil.formatPrice(avgPrice))
                .build();
        // 将购物车信息存入缓存，设置过期时间（30分钟）
        redisCacheUtil.set(cacheKey, cartResponse, USER_SHOP_CART_CACHE_TIME);
        return cartResponse;
    }


    @Override
    public EditSizeDetailResponse getCartItem(EditCartItemRequest request) {
        log.info("获取购物车商品项信息, request: {}", request);
        // 构建缓存key
        String cacheKey = String.format(USER_SHOP_CART_ITEM_CACHE_KEY_PREFIX, request.getUserId(), request.getSku(), request.getSize());
        // 尝试从缓存中获取购物车商品项信息
        Object cachedCartItem = redisCacheUtil.get(cacheKey);
        if (cachedCartItem != null) {
            log.info("从缓存中获取购物车商品项信息, userId: {}, sku: {}, size: {}", request.getUserId(), request.getSku(), request.getSize());
            return (EditSizeDetailResponse) cachedCartItem;
        }
        // 缓存中没有，使用SQL联表查询获取购物车商品项详情
        log.info("从数据库中获取购物车商品项信息, userId: {}, sku: {}, size: {}", request.getUserId(), request.getSku(), request.getSize());
        List<CartQueryResult> cartDetails = iSysShopCartService.queryCartItem(request);
        // 如果查询结果为空，返回空响应
        if (CollUtil.isEmpty(cartDetails)) {
            return null;
        }
        // 单价分转美元
        cartDetails.forEach(detail -> {
            if (detail.getUnitPrice() == null) {
                detail.setUnitPrice("0.00");
            } else {
                detail.setUnitPrice(PriceFormatUtil.formatCentsToYuan(detail.getUnitPrice()));
            }
        });
        // 获取购物车ID和商品项ID
        CartQueryResult cartQueryResult = cartDetails.get(0);
        // 构建尺码明细响应
        List<SizeDetailResponse> sizeDetailResponses = new ArrayList<>();
        int totalQuantity = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;
        // 遍历每个尺码明细
        for (CartQueryResult detail : cartDetails) {
            if (detail.getSize() == null) {
                continue;
            }
            BigDecimal subtotalInDollars = new BigDecimal(detail.getUnitPrice()).multiply(new BigDecimal(detail.getQuantity()));
            totalQuantity += detail.getQuantity();
            totalAmount = totalAmount.add(subtotalInDollars);
            SizeDetailResponse sizeDetailResponse = SizeDetailResponse.createSizeDetailResponse(detail, cartQueryResult.getItemId());
            sizeDetailResponses.add(sizeDetailResponse);
        }
        EditCartItemResponse itemResponse = EditCartItemResponse.builder()
                .itemId(cartQueryResult.getItemId())
                .detailId(cartQueryResult.getDetailId())
                .cartId(cartQueryResult.getCartId())
                .productName(cartQueryResult.getProductName())
                .sku(cartQueryResult.getSku())
                .imageUrl(cartQueryResult.getImageUrl())
                .sizeDetails(sizeDetailResponses)
                .totalQuantity(totalQuantity)
                .totalAmount(PriceFormatUtil.formatPrice(totalAmount))
                .build();
        BigDecimal avgPrice = totalQuantity > 0 ? totalAmount.divide(new BigDecimal(totalQuantity), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
        List<EditCartItemResponse> itemResponses = new ArrayList<>();
        itemResponses.add(itemResponse);
        EditSizeDetailResponse result = EditSizeDetailResponse.builder()
                .details(itemResponses)
                .totalQuantity(totalQuantity)
                .avgPrice(PriceFormatUtil.formatPrice(avgPrice))
                .totalPrice(PriceFormatUtil.formatPrice(totalAmount))
                .build();
        redisCacheUtil.set(cacheKey, result, 1800);
        return result;
    }


    /**
     * 构建尺码明细聚合信息
     *
     * @param itemDetails 商品项详情列表
     * @return 尺码明细聚合信息列表
     */
    private List<SizeDetailSumResponse> buildSizeDetailSums(List<CartQueryResult> itemDetails) {
        Map<String, List<CartQueryResult>> sizeMap = itemDetails
                .stream()
                .filter(detail -> detail.getSize() != null)
                .collect(Collectors.groupingBy(CartQueryResult::getSize));
        List<SizeDetailSumResponse> sizeDetailSums = new ArrayList<>();
        // 遍历每个尺码组
        for (Map.Entry<String, List<CartQueryResult>> entry : sizeMap.entrySet()) {
            String size = entry.getKey();
            List<CartQueryResult> sizeDetails = entry.getValue();
            // 计算该尺码的总数量和总金额
            int totalQuantity = sizeDetails.stream().mapToInt(CartQueryResult::getQuantity).sum();
            BigDecimal totalPrice = BigDecimal.ZERO;
            for (CartQueryResult detail : sizeDetails) {
                double unitPrice = Double.parseDouble(detail.getUnitPrice());
                double subtotal = unitPrice * Long.valueOf(detail.getQuantity());
                totalPrice = totalPrice.add(BigDecimal.valueOf(subtotal));
            }
            // 计算平均价格并格式化为两位小数
            BigDecimal avgPrice = totalQuantity > 0 ? totalPrice.divide(new BigDecimal(totalQuantity), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            Long itemId = sizeDetails.get(0).getItemId();
            SizeDetailSumResponse sizeDetailSum = SizeDetailSumResponse
                    .builder()
                    .cartItemId(itemId)
                    .size(size)
                    .quantity(totalQuantity)
                    .avgPrice(PriceFormatUtil.formatPrice(avgPrice))
                    .totalPrice(PriceFormatUtil.formatPrice(totalPrice))
                    .build();
            sizeDetailSums.add(sizeDetailSum);
        }
        return sizeDetailSums;
    }
}
