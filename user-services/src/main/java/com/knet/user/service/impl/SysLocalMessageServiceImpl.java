package com.knet.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.user.mapper.SysLocalMessageMapper;
import com.knet.user.model.dto.req.SaveLocalMessageRequest;
import com.knet.user.model.entity.SysLocalMessage;
import com.knet.user.model.enums.MessageStatus;
import com.knet.user.service.ISysLocalMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/1/15 10:25
 * @description: 本地消息表服务实现
 */
@Slf4j
@Service
public class SysLocalMessageServiceImpl extends ServiceImpl<SysLocalMessageMapper, SysLocalMessage> 
        implements ISysLocalMessageService {

    @Resource
    private SysLocalMessageMapper sysLocalMessageMapper;
    
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 重试间隔时间（分钟）：1, 5, 15
     */
    private static final int[] RETRY_INTERVALS = {1, 5, 15};

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysLocalMessage saveLocalMessage(SaveLocalMessageRequest request) {
        log.info("保存本地消息: messageId={}, businessType={}", request.getMessageId(), request.getBusinessType());
        
        // 检查消息是否已存在
        LambdaQueryWrapper<SysLocalMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysLocalMessage::getMessageId, request.getMessageId());
        SysLocalMessage existingMessage = this.getOne(queryWrapper);
        if (existingMessage != null) {
            log.warn("消息已存在: messageId={}", request.getMessageId());
            return existingMessage;
        }

        // 构建消息实体
        SysLocalMessage message = SysLocalMessage.builder()
                .messageId(request.getMessageId())
                .exchange(request.getExchange())
                .routingKey(request.getRoutingKey())
                .messageBody(request.getMessageBody())
                .messageHeaders(request.getMessageHeaders() != null ? JSON.toJSONString(request.getMessageHeaders()) : null)
                .status(MessageStatus.PENDING)
                .retryCount(0)
                .maxRetryCount(request.getMaxRetryCount())
                .businessType(request.getBusinessType())
                .businessId(request.getBusinessId())
                .remark(request.getRemark())
                .build();

        // 保存消息
        boolean saved = this.save(message);
        if (!saved) {
            throw new RuntimeException("保存本地消息失败: messageId=" + request.getMessageId());
        }

        log.info("本地消息保存成功: messageId={}", request.getMessageId());
        return message;
    }

    @Override
    public boolean sendMessage(String messageId) {
        log.info("开始发送消息: messageId={}", messageId);
        
        // 查询消息
        LambdaQueryWrapper<SysLocalMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysLocalMessage::getMessageId, messageId);
        SysLocalMessage message = this.getOne(queryWrapper);
        
        if (message == null) {
            log.error("消息不存在: messageId={}", messageId);
            return false;
        }

        if (MessageStatus.SUCCESS.equals(message.getStatus())) {
            log.info("消息已发送成功，跳过: messageId={}", messageId);
            return true;
        }

        // 更新状态为发送中
        int updateResult = sysLocalMessageMapper.updateStatusToSending(messageId, message.getVersion());
        if (updateResult == 0) {
            log.warn("更新消息状态为发送中失败，可能存在并发操作: messageId={}", messageId);
            return false;
        }

        try {
            // 发送消息到RabbitMQ
            boolean sendResult = doSendMessage(message);
            
            if (sendResult) {
                // 发送成功，更新状态
                markMessageSuccess(messageId, message.getVersion() + 1);
                log.info("消息发送成功: messageId={}", messageId);
                return true;
            } else {
                // 发送失败，更新状态
                markMessageFailed(messageId, message.getVersion() + 1, "RabbitMQ发送失败");
                log.error("消息发送失败: messageId={}", messageId);
                return false;
            }
        } catch (Exception e) {
            // 发送异常，更新状态
            markMessageFailed(messageId, message.getVersion() + 1, "发送异常: " + e.getMessage());
            log.error("消息发送异常: messageId={}", messageId, e);
            return false;
        }
    }

    @Override
    public int batchSendMessages(List<String> messageIds) {
        if (CollUtil.isEmpty(messageIds)) {
            return 0;
        }

        int successCount = 0;
        for (String messageId : messageIds) {
            try {
                if (sendMessage(messageId)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量发送消息失败: messageId={}", messageId, e);
            }
        }

        log.info("批量发送消息完成: 总数={}, 成功={}", messageIds.size(), successCount);
        return successCount;
    }

    @Override
    public List<SysLocalMessage> getRetryMessages(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 100; // 默认限制100条
        }
        
        return sysLocalMessageMapper.selectRetryMessages(MessageStatus.PENDING, new Date(), limit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int processRetryMessages(Integer limit) {
        log.info("开始处理重试消息，限制条数: {}", limit);
        
        List<SysLocalMessage> retryMessages = getRetryMessages(limit);
        if (CollUtil.isEmpty(retryMessages)) {
            log.info("没有需要重试的消息");
            return 0;
        }

        int processedCount = 0;
        for (SysLocalMessage message : retryMessages) {
            try {
                if (sendMessage(message.getMessageId())) {
                    processedCount++;
                }
            } catch (Exception e) {
                log.error("处理重试消息失败: messageId={}", message.getMessageId(), e);
            }
        }

        log.info("重试消息处理完成: 总数={}, 成功={}", retryMessages.size(), processedCount);
        return processedCount;
    }

    @Override
    public boolean markMessageSuccess(String messageId, Integer version) {
        Date sentTime = new Date();
        int updateResult = sysLocalMessageMapper.updateStatusToSuccess(messageId, sentTime, version);
        return updateResult > 0;
    }

    @Override
    public boolean markMessageFailed(String messageId, Integer version, String failureReason) {
        // 查询当前消息信息
        LambdaQueryWrapper<SysLocalMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysLocalMessage::getMessageId, messageId);
        SysLocalMessage message = this.getOne(queryWrapper);
        
        if (message == null) {
            log.error("标记消息失败时，消息不存在: messageId={}", messageId);
            return false;
        }

        int newRetryCount = message.getRetryCount() + 1;
        Date lastRetryTime = new Date();
        Date nextRetryTime = null;
        MessageStatus newStatus = MessageStatus.PENDING;

        // 判断是否超过最大重试次数
        if (newRetryCount >= message.getMaxRetryCount()) {
            newStatus = MessageStatus.FAILED;
            log.warn("消息重试次数已达上限，标记为最终失败: messageId={}, retryCount={}", 
                    messageId, newRetryCount);
        } else {
            // 计算下次重试时间（指数退避）
            int intervalIndex = Math.min(newRetryCount - 1, RETRY_INTERVALS.length - 1);
            int intervalMinutes = RETRY_INTERVALS[intervalIndex];
            nextRetryTime = new Date(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(intervalMinutes));
            log.info("消息将在{}分钟后重试: messageId={}, retryCount={}", 
                    intervalMinutes, messageId, newRetryCount);
        }

        int updateResult = sysLocalMessageMapper.updateStatusToFailed(
                messageId, newRetryCount, nextRetryTime, lastRetryTime, 
                failureReason, newStatus, version);
        
        return updateResult > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAndSendMessage(SaveLocalMessageRequest request) {
        // 先保存消息
        SysLocalMessage savedMessage = saveLocalMessage(request);
        
        // 立即尝试发送
        return sendMessage(savedMessage.getMessageId());
    }

    /**
     * 实际发送消息到RabbitMQ
     */
    private boolean doSendMessage(SysLocalMessage localMessage) {
        try {
            // 构建消息属性
            MessageProperties properties = new MessageProperties();
            properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            properties.setHeader("messageId", localMessage.getMessageId());
            properties.setHeader("routingKey", localMessage.getRoutingKey());
            
            // 添加自定义消息头
            if (StrUtil.isNotBlank(localMessage.getMessageHeaders())) {
                Map<String, Object> headers = JSON.parseObject(localMessage.getMessageHeaders(), Map.class);
                if (headers != null) {
                    headers.forEach(properties::setHeader);
                }
            }

            // 构建消息
            Message message = new Message(localMessage.getMessageBody().getBytes(StandardCharsets.UTF_8), properties);
            
            // 发送消息
            CorrelationData correlationData = new CorrelationData(localMessage.getMessageId());
            rabbitTemplate.convertAndSend(
                    localMessage.getExchange(),
                    localMessage.getRoutingKey(),
                    message,
                    correlationData
            );

            // 等待确认结果
            CorrelationData.Confirm confirm = correlationData.getFuture().get(5, TimeUnit.SECONDS);
            return confirm != null && confirm.isAck();
            
        } catch (Exception e) {
            log.error("发送消息到RabbitMQ失败: messageId={}", localMessage.getMessageId(), e);
            return false;
        }
    }
}
