package com.knet.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.user.mapper.SysShopCartSizeDetailMapper;
import com.knet.user.model.dto.req.AddToCartRequest;
import com.knet.user.model.entity.SysShopCartSizeDetail;
import com.knet.user.service.ISysShopCartSizeDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【sys_shop_cart_size_detail(购物车尺码明细表)】的数据库操作Service实现
 * @date 2025-05-21 10:14:37
 */
@Slf4j
@Service
public class SysShopCartSizeDetailServiceImpl extends ServiceImpl<SysShopCartSizeDetailMapper, SysShopCartSizeDetail> implements ISysShopCartSizeDetailService {

    /**
     * 获取或购物车尺码明细记录
     * (SIZE,UNIT_PRICE 视为一条记录)
     *
     * @param sizeDetail 添加购物车请求中的尺码明细
     * @param cartItemId 购物车商品项id
     * @return 购物车尺码明细记录
     */
    @Override
    public SysShopCartSizeDetail getSysShopCartSizeDetail(AddToCartRequest.SizeDetailRequest sizeDetail, Long cartItemId) {
        LambdaQueryWrapper<SysShopCartSizeDetail> sizeDetailQueryWrapper = new LambdaQueryWrapper<>();
        sizeDetailQueryWrapper
                .eq(SysShopCartSizeDetail::getCartItemId, cartItemId)
                .eq(SysShopCartSizeDetail::getSize, sizeDetail.getSize())
                .eq(SysShopCartSizeDetail::getUnitPrice, Long.valueOf(sizeDetail.getUnitPrice()))
                .eq(SysShopCartSizeDetail::getSelected, 1);
        return this.getOne(sizeDetailQueryWrapper);
    }
}




