package com.knet.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.OperationResult;
import com.knet.common.enums.UserOperationType;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RandomStrUtil;
import com.knet.user.mapper.SysUserOperationRecordMapper;
import com.knet.user.model.dto.req.CreateUserOperationRecordRequest;
import com.knet.user.model.entity.SysUserOperationRecord;
import com.knet.user.service.ISysUserOperationRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 针对表【sys_user_operation_record(用户操作记录表)】的数据库操作Service实现
 * @date 2025-05-20 18:40:00
 */
@Slf4j
@Service
public class SysUserOperationRecordServiceImpl extends ServiceImpl<SysUserOperationRecordMapper, SysUserOperationRecord> implements ISysUserOperationRecordService {

    @Resource
    private RandomStrUtil randomStrUtil;

    @Override
    public String createOperationRecord(CreateUserOperationRecordRequest request) {
        log.info("创建用户操作记录: userId={}, operationType={}, operationResult={}",
                request.getUserId(), request.getOperationType(), request.getOperationResult());
        String operationId = randomStrUtil.getOperatedId();
        SysUserOperationRecord record = SysUserOperationRecord.builder()
                .operationId(operationId)
                .userId(request.getUserId())
                .operatorId(request.getOperatorId())
                .operatorType(request.getOperatorType())
                .operationType(request.getOperationType())
                .operationResult(request.getOperationResult())
                .operationDesc(request.getOperationDesc())
                .operationDetail(request.getOperationDetail())
                .clientIp(request.getClientIp())
                .userAgent(request.getUserAgent())
                .businessId(request.getBusinessId())
                .businessType(request.getBusinessType())
                .errorMessage(request.getErrorMessage())
                .remarks(request.getRemarks())
                .build();
        boolean saved = this.save(record);
        if (!saved) {
            log.error("用户操作记录保存失败: operationId={}", operationId);
            throw new ServiceException("用户操作记录保存失败");
        }
        log.info("用户操作记录创建成功: operationId={}", operationId);
        return operationId;
    }

    @Override
    public String createOperationRecord(Long userId, Long operatorId, String operatorType,
                                        UserOperationType operationType, OperationResult operationResult,
                                        String operationDesc, String businessId, String businessType) {
        CreateUserOperationRecordRequest request = CreateUserOperationRecordRequest
                .builder()
                .userId(userId)
                .operatorId(operatorId)
                .operatorType(operatorType)
                .operationType(operationType)
                .operationResult(operationResult)
                .operationDesc(operationDesc)
                .businessId(businessId)
                .businessType(businessType)
                .build();
        return createOperationRecord(request);
    }

    @Override
    public String createOperationRecord(Long userId, UserOperationType operationType,
                                        OperationResult operationResult, String operationDesc) {
        return createOperationRecord(userId, userId, "USER", operationType, operationResult,
                operationDesc, null, null);
    }
}
