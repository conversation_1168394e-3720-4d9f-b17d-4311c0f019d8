package com.knet.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.user.model.dto.req.EditCartItemRequest;
import com.knet.user.model.dto.rsp.CartQueryResult;
import com.knet.user.model.entity.SysShopCart;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22 14:10
 * @description: SysShopCartService 购物车服务
 */
public interface ISysShopCartService extends IService<SysShopCart> {

    /**
     * 获取或创建购物车
     *
     * @param userId 用户ID
     * @return 购物车
     */
    SysShopCart getOrCreateCart(Long userId);

    /**
     * 查询购物车详情
     *
     * @param userId 用户ID
     * @return 购物车详情列表
     */
    List<CartQueryResult> queryCartDetails(Long userId);

    /**
     * 查询购物车中的特定商品
     *
     * @param request 请求
     * @return 购物车商品详情
     */
    List<CartQueryResult> queryCartItem(EditCartItemRequest request);

    /**
     * 删除购物车
     *
     * @param userId 用户ID
     */
    void deleteCart(Long userId);
}
