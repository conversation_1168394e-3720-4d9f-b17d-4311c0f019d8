package com.knet.user.service;

import com.knet.user.model.dto.req.AddToCartRequest;
import com.knet.user.model.dto.req.EditCartItemRequest;
import com.knet.user.model.dto.req.RemoveFromCartRequest;
import com.knet.user.model.dto.req.UpdateCartRequest;
import com.knet.user.model.dto.rsp.CartResponse;
import com.knet.user.model.dto.rsp.EditSizeDetailResponse;

/**
 * <AUTHOR>
 * @date 2025/5/21 11:48
 * @description: 购物车服务定义
 */
public interface IShopCartService {
    /**
     * 添加商品到购物车
     *
     * @param request 添加商品请求
     * @return 购物车信息
     */
    CartResponse addToCart(AddToCartRequest request);

    /**
     * 更新购物车商品
     *
     * @param request 更新购物车请求
     */
    void updateCart(UpdateCartRequest request);

    /**
     * 从购物车移除商品
     *
     * @param request 移除商品请求
     */
    void removeFromCart(RemoveFromCartRequest request);

    /**
     * 获取购物车信息
     *
     * @param userId 用户ID
     * @return 购物车信息
     */
    CartResponse getCart(Long userId);

    /**
     * 获取购物车-购物车商品项-适用于购物车编辑
     *
     * @param request re
     * @return 购物车商品项信息
     */
    EditSizeDetailResponse getCartItem(EditCartItemRequest request);
}
