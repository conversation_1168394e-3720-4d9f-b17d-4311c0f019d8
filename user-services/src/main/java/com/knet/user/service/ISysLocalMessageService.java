package com.knet.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.user.model.dto.req.SaveLocalMessageRequest;
import com.knet.user.model.entity.SysLocalMessage;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/15 10:20
 * @description: 本地消息表服务接口
 */
public interface ISysLocalMessageService extends IService<SysLocalMessage> {

    /**
     * 保存本地消息
     * 
     * @param request 保存消息请求
     * @return 保存的消息实体
     */
    SysLocalMessage saveLocalMessage(SaveLocalMessageRequest request);

    /**
     * 发送消息并更新状态
     * 
     * @param messageId 消息ID
     * @return 是否发送成功
     */
    boolean sendMessage(String messageId);

    /**
     * 批量发送消息
     * 
     * @param messageIds 消息ID列表
     * @return 发送成功的消息数量
     */
    int batchSendMessages(List<String> messageIds);

    /**
     * 获取需要重试的消息
     * 
     * @param limit 限制条数
     * @return 需要重试的消息列表
     */
    List<SysLocalMessage> getRetryMessages(Integer limit);

    /**
     * 处理重试消息
     * 
     * @param limit 每次处理的消息数量限制
     * @return 处理的消息数量
     */
    int processRetryMessages(Integer limit);

    /**
     * 标记消息发送成功
     * 
     * @param messageId 消息ID
     * @param version 版本号
     * @return 是否更新成功
     */
    boolean markMessageSuccess(String messageId, Integer version);

    /**
     * 标记消息发送失败
     * 
     * @param messageId 消息ID
     * @param version 版本号
     * @param failureReason 失败原因
     * @return 是否更新成功
     */
    boolean markMessageFailed(String messageId, Integer version, String failureReason);

    /**
     * 保存消息并立即尝试发送
     * 
     * @param request 保存消息请求
     * @return 是否发送成功
     */
    boolean saveAndSendMessage(SaveLocalMessageRequest request);
}
