package com.knet.user.system.schedule;

import com.knet.user.service.ISysLocalMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2025/1/15 11:00
 * @description: 本地消息重试定时任务
 */
@Slf4j
@Component
public class LocalMessageRetryJob {

    @Resource
    private ISysLocalMessageService sysLocalMessageService;

    /**
     * 本地消息重试任务
     * 每5分钟执行一次，处理失败的消息重试
     * 
     * 任务参数格式（可选）：
     * - 空参数：使用默认配置（每次处理100条消息）
     * - JSON格式：{"limit": 200} 指定每次处理的消息数量
     */
    @XxlJob("localMessageRetryJob")
    public ReturnT<String> localMessageRetryJob(String param) {
        log.info("本地消息重试任务开始执行，参数: {}", param);
        String time = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        XxlJobHelper.log("XXL-JOB 本地消息重试任务触发时间: {}", time);
        
        StopWatch stopWatch = new StopWatch("localMessageRetryJob");
        stopWatch.start();
        
        try {
            // 解析任务参数
            int limit = parseLimit(param);
            
            // 处理重试消息
            int processedCount = sysLocalMessageService.processRetryMessages(limit);
            
            stopWatch.stop();
            String resultMsg = String.format(
                "本地消息重试任务执行成功，处理消息数量: %d，耗时: %d ms", 
                processedCount, stopWatch.getTotalTimeMillis()
            );
            
            log.info(resultMsg);
            XxlJobHelper.log(resultMsg);
            
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            stopWatch.stop();
            String errorMsg = String.format(
                "本地消息重试任务执行失败，耗时: %d ms，错误信息: %s", 
                stopWatch.getTotalTimeMillis(), e.getMessage()
            );
            
            log.error(errorMsg, e);
            XxlJobHelper.handleFail(errorMsg);
            
            return ReturnT.FAIL;
        }
    }

    /**
     * 清理过期的成功消息
     * 每天凌晨2点执行一次，清理7天前的成功消息
     * 
     * 任务参数格式（可选）：
     * - 空参数：清理7天前的成功消息
     * - 数字：指定清理天数，如 "30" 表示清理30天前的消息
     */
    @XxlJob("cleanSuccessMessagesJob")
    public ReturnT<String> cleanSuccessMessagesJob(String param) {
        log.info("清理成功消息任务开始执行，参数: {}", param);
        String time = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        XxlJobHelper.log("XXL-JOB 清理成功消息任务触发时间: {}", time);
        
        StopWatch stopWatch = new StopWatch("cleanSuccessMessagesJob");
        stopWatch.start();
        
        try {
            // 解析清理天数
            int days = parseDays(param);
            
            // TODO: 实现清理逻辑
            // 这里可以添加清理成功消息的逻辑
            // 例如：删除指定天数前的成功消息记录
            
            stopWatch.stop();
            String resultMsg = String.format(
                "清理成功消息任务执行成功，清理%d天前的消息，耗时: %d ms", 
                days, stopWatch.getTotalTimeMillis()
            );
            
            log.info(resultMsg);
            XxlJobHelper.log(resultMsg);
            
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            stopWatch.stop();
            String errorMsg = String.format(
                "清理成功消息任务执行失败，耗时: %d ms，错误信息: %s", 
                stopWatch.getTotalTimeMillis(), e.getMessage()
            );
            
            log.error(errorMsg, e);
            XxlJobHelper.handleFail(errorMsg);
            
            return ReturnT.FAIL;
        }
    }

    /**
     * 解析任务参数中的limit值
     */
    private int parseLimit(String param) {
        int defaultLimit = 100;
        
        if (param == null || param.trim().isEmpty()) {
            return defaultLimit;
        }
        
        try {
            // 尝试解析为JSON
            if (param.trim().startsWith("{")) {
                // 简单的JSON解析，实际项目中建议使用JSON库
                String limitStr = param.replaceAll("[{}\"\\s]", "")
                                      .replace("limit:", "");
                return Integer.parseInt(limitStr);
            } else {
                // 直接解析为数字
                return Integer.parseInt(param.trim());
            }
        } catch (Exception e) {
            log.warn("解析任务参数失败，使用默认值: {}, 参数: {}", defaultLimit, param);
            XxlJobHelper.log("解析任务参数失败，使用默认值: {}, 参数: {}", defaultLimit, param);
            return defaultLimit;
        }
    }

    /**
     * 解析清理天数参数
     */
    private int parseDays(String param) {
        int defaultDays = 7;
        
        if (param == null || param.trim().isEmpty()) {
            return defaultDays;
        }
        
        try {
            return Integer.parseInt(param.trim());
        } catch (Exception e) {
            log.warn("解析清理天数参数失败，使用默认值: {}, 参数: {}", defaultDays, param);
            XxlJobHelper.log("解析清理天数参数失败，使用默认值: {}, 参数: {}", defaultDays, param);
            return defaultDays;
        }
    }
}
