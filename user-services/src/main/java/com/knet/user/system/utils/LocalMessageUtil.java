package com.knet.user.system.utils;

import cn.hutool.core.util.RandomUtil;
import com.knet.user.model.dto.req.SaveLocalMessageRequest;
import com.knet.user.service.ISysLocalMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/15 11:20
 * @description: 本地消息工具类 - 提供便捷的消息保存方法
 */
@Slf4j
@Component
public class LocalMessageUtil {

    @Resource
    private ISysLocalMessageService sysLocalMessageService;

    /**
     * 保存订单相关消息
     * 
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param businessId 业务ID
     * @return 是否保存成功
     */
    public boolean saveOrderMessage(String routingKey, String messageBody, String businessId) {
        return saveOrderMessage(routingKey, messageBody, businessId, null);
    }

    /**
     * 保存订单相关消息
     * 
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param businessId 业务ID
     * @param remark 备注
     * @return 是否保存成功
     */
    public boolean saveOrderMessage(String routingKey, String messageBody, String businessId, String remark) {
        String messageId = generateOrderMessageId();
        Map<String, Object> headers = new HashMap<>();
        headers.put("routingKey", routingKey);
        headers.put("messageId", messageId);

        SaveLocalMessageRequest request = SaveLocalMessageRequest.builder()
                .messageId(messageId)
                .exchange("order-exchange")
                .routingKey(routingKey)
                .messageBody(messageBody)
                .messageHeaders(headers)
                .businessType("order")
                .businessId(businessId)
                .remark(remark)
                .build();

        try {
            sysLocalMessageService.saveLocalMessage(request);
            return true;
        } catch (Exception e) {
            log.error("保存订单消息失败: messageId={}, businessId={}", messageId, businessId, e);
            return false;
        }
    }

    /**
     * 保存用户相关消息
     * 
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param businessId 业务ID
     * @return 是否保存成功
     */
    public boolean saveUserMessage(String routingKey, String messageBody, String businessId) {
        return saveUserMessage(routingKey, messageBody, businessId, null);
    }

    /**
     * 保存用户相关消息
     * 
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param businessId 业务ID
     * @param remark 备注
     * @return 是否保存成功
     */
    public boolean saveUserMessage(String routingKey, String messageBody, String businessId, String remark) {
        String messageId = generateUserMessageId();
        Map<String, Object> headers = new HashMap<>();
        headers.put("routingKey", routingKey);
        headers.put("messageId", messageId);

        SaveLocalMessageRequest request = SaveLocalMessageRequest.builder()
                .messageId(messageId)
                .exchange("user-operation-exchange")
                .routingKey(routingKey)
                .messageBody(messageBody)
                .messageHeaders(headers)
                .businessType("user")
                .businessId(businessId)
                .remark(remark)
                .build();

        try {
            sysLocalMessageService.saveLocalMessage(request);
            return true;
        } catch (Exception e) {
            log.error("保存用户消息失败: messageId={}, businessId={}", messageId, businessId, e);
            return false;
        }
    }

    /**
     * 保存支付相关消息
     * 
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param businessId 业务ID
     * @return 是否保存成功
     */
    public boolean savePaymentMessage(String routingKey, String messageBody, String businessId) {
        return savePaymentMessage(routingKey, messageBody, businessId, null);
    }

    /**
     * 保存支付相关消息
     * 
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param businessId 业务ID
     * @param remark 备注
     * @return 是否保存成功
     */
    public boolean savePaymentMessage(String routingKey, String messageBody, String businessId, String remark) {
        String messageId = generatePaymentMessageId();
        Map<String, Object> headers = new HashMap<>();
        headers.put("routingKey", routingKey);
        headers.put("messageId", messageId);

        SaveLocalMessageRequest request = SaveLocalMessageRequest.builder()
                .messageId(messageId)
                .exchange("payment-exchange")
                .routingKey(routingKey)
                .messageBody(messageBody)
                .messageHeaders(headers)
                .businessType("payment")
                .businessId(businessId)
                .remark(remark)
                .build();

        try {
            sysLocalMessageService.saveLocalMessage(request);
            return true;
        } catch (Exception e) {
            log.error("保存支付消息失败: messageId={}, businessId={}", messageId, businessId, e);
            return false;
        }
    }

    /**
     * 保存并立即发送消息
     * 
     * @param exchange 交换机
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 是否发送成功
     */
    public boolean saveAndSendMessage(String exchange, String routingKey, String messageBody, 
                                    String businessType, String businessId) {
        return saveAndSendMessage(exchange, routingKey, messageBody, businessType, businessId, null, null);
    }

    /**
     * 保存并立即发送消息
     * 
     * @param exchange 交换机
     * @param routingKey 路由键
     * @param messageBody 消息体
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param headers 消息头
     * @param remark 备注
     * @return 是否发送成功
     */
    public boolean saveAndSendMessage(String exchange, String routingKey, String messageBody, 
                                    String businessType, String businessId, 
                                    Map<String, Object> headers, String remark) {
        String messageId = generateMessageId(businessType);
        
        if (headers == null) {
            headers = new HashMap<>();
        }
        headers.put("routingKey", routingKey);
        headers.put("messageId", messageId);

        SaveLocalMessageRequest request = SaveLocalMessageRequest.builder()
                .messageId(messageId)
                .exchange(exchange)
                .routingKey(routingKey)
                .messageBody(messageBody)
                .messageHeaders(headers)
                .businessType(businessType)
                .businessId(businessId)
                .remark(remark)
                .build();

        try {
            return sysLocalMessageService.saveAndSendMessage(request);
        } catch (Exception e) {
            log.error("保存并发送消息失败: messageId={}, businessType={}, businessId={}", 
                    messageId, businessType, businessId, e);
            return false;
        }
    }

    /**
     * 生成订单消息ID
     */
    private String generateOrderMessageId() {
        return String.format("KNET_B2B_ORDER_MSG_%s", RandomUtil.randomString(16));
    }

    /**
     * 生成用户消息ID
     */
    private String generateUserMessageId() {
        return String.format("KNET_B2B_USER_MSG_%s", RandomUtil.randomString(16));
    }

    /**
     * 生成支付消息ID
     */
    private String generatePaymentMessageId() {
        return String.format("KNET_B2B_PAYMENT_MSG_%s", RandomUtil.randomString(16));
    }

    /**
     * 根据业务类型生成消息ID
     */
    private String generateMessageId(String businessType) {
        return String.format("KNET_B2B_%s_MSG_%s", 
                businessType.toUpperCase(), RandomUtil.randomString(16));
    }
}
