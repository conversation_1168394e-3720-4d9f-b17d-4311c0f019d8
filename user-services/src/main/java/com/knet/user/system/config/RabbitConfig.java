package com.knet.user.system.config;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/5/28 11:00
 * @description: RabbitMQ配置 - 用户操作记录消费端
 */
@Configuration
public class RabbitConfig {

    /**
     * 用户操作记录交换机
     */
    @Bean
    public TopicExchange userOperationExchange() {
        return new TopicExchange("user-operation-exchange", true, false);
    }

    /**
     * 用户操作记录队列
     */
    @Bean
    public Queue userOperationQueue() {
        return QueueBuilder
                .durable("user-operation-queue." + "user-services")
                .withArgument("x-dead-letter-exchange", "DLX")
                .withArgument("x-dead-letter-routing-key", "user.operation.*")
                .build();
    }

    /**
     * 队列绑定
     */
    @Bean
    public Binding userOperationBinding() {
        return BindingBuilder
                .bind(userOperationQueue())
                .to(userOperationExchange())
                .with("user.operation.*");
    }

    /**
     * 死信交换机
     */
    @Bean
    public DirectExchange dlxExchange() {
        return new DirectExchange("DLX", true, false);
    }

    /**
     * 死信队列
     */
    @Bean
    public Queue dlxQueue() {
        return QueueBuilder
                .durable("dlx.user.operation.queue")
                .build();
    }

    /**
     * 死信绑定
     */
    @Bean
    public Binding dlxBinding() {
        return BindingBuilder
                .bind(dlxQueue())
                .to(dlxExchange())
                .with("user.operation.*");
    }
}
