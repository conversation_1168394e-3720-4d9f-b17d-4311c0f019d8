package com.knet.user.service;

import com.knet.user.model.dto.req.SaveLocalMessageRequest;
import com.knet.user.model.entity.SysLocalMessage;
import com.knet.user.model.enums.MessageStatus;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2025/1/15 12:00
 * @description: 本地消息表服务测试
 */
@SpringBootTest
@ActiveProfiles("dev")
public class LocalMessageServiceTest {

    @Resource
    private ISysLocalMessageService sysLocalMessageService;

    @Test
    public void testSaveLocalMessage() {
        // 准备测试数据
        Map<String, Object> headers = new HashMap<>();
        headers.put("routingKey", "order.created");
        headers.put("messageId", "TEST_MSG_001");

        SaveLocalMessageRequest request = SaveLocalMessageRequest.builder()
                .messageId("TEST_MSG_001")
                .exchange("order-exchange")
                .routingKey("order.created")
                .messageBody("{\"orderId\":\"ORDER_001\",\"userId\":\"USER_001\"}")
                .messageHeaders(headers)
                .businessType("order")
                .businessId("ORDER_001")
                .remark("测试消息")
                .build();

        // 执行保存
        SysLocalMessage savedMessage = sysLocalMessageService.saveLocalMessage(request);

        // 验证结果
        assertNotNull(savedMessage);
        assertEquals("TEST_MSG_001", savedMessage.getMessageId());
        assertEquals("order-exchange", savedMessage.getExchange());
        assertEquals("order.created", savedMessage.getRoutingKey());
        assertEquals(MessageStatus.PENDING, savedMessage.getStatus());
        assertEquals(0, savedMessage.getRetryCount());
        assertEquals(3, savedMessage.getMaxRetryCount());
    }

    @Test
    public void testSaveAndSendMessage() {
        // 准备测试数据
        SaveLocalMessageRequest request = SaveLocalMessageRequest.builder()
                .messageId("TEST_MSG_002")
                .exchange("order-exchange")
                .routingKey("order.created")
                .messageBody("{\"orderId\":\"ORDER_002\",\"userId\":\"USER_002\"}")
                .businessType("order")
                .businessId("ORDER_002")
                .remark("测试保存并发送消息")
                .build();

        // 执行保存并发送
        boolean result = sysLocalMessageService.saveAndSendMessage(request);

        // 验证结果（由于测试环境可能没有RabbitMQ，这里主要验证保存逻辑）
        // 实际结果取决于RabbitMQ的可用性
        System.out.println("保存并发送消息结果: " + result);
    }

    @Test
    public void testGetRetryMessages() {
        // 获取需要重试的消息
        List<SysLocalMessage> retryMessages = sysLocalMessageService.getRetryMessages(10);

        // 验证结果
        assertNotNull(retryMessages);
        System.out.println("需要重试的消息数量: " + retryMessages.size());

        for (SysLocalMessage message : retryMessages) {
            System.out.println("消息ID: " + message.getMessageId() + 
                             ", 状态: " + message.getStatus() + 
                             ", 重试次数: " + message.getRetryCount());
        }
    }

    @Test
    public void testProcessRetryMessages() {
        // 处理重试消息
        int processedCount = sysLocalMessageService.processRetryMessages(5);

        // 验证结果
        assertTrue(processedCount >= 0);
        System.out.println("处理的重试消息数量: " + processedCount);
    }

    @Test
    public void testMarkMessageSuccess() {
        // 先保存一条消息
        SaveLocalMessageRequest request = SaveLocalMessageRequest.builder()
                .messageId("TEST_MSG_SUCCESS")
                .exchange("order-exchange")
                .routingKey("order.created")
                .messageBody("{\"orderId\":\"ORDER_SUCCESS\",\"userId\":\"USER_SUCCESS\"}")
                .businessType("order")
                .businessId("ORDER_SUCCESS")
                .remark("测试标记成功")
                .build();

        SysLocalMessage savedMessage = sysLocalMessageService.saveLocalMessage(request);

        // 标记为成功
        boolean result = sysLocalMessageService.markMessageSuccess(
                savedMessage.getMessageId(), 
                savedMessage.getVersion()
        );

        // 验证结果
        assertTrue(result);
        System.out.println("标记消息成功结果: " + result);
    }

    @Test
    public void testMarkMessageFailed() {
        // 先保存一条消息
        SaveLocalMessageRequest request = SaveLocalMessageRequest.builder()
                .messageId("TEST_MSG_FAILED")
                .exchange("order-exchange")
                .routingKey("order.created")
                .messageBody("{\"orderId\":\"ORDER_FAILED\",\"userId\":\"USER_FAILED\"}")
                .businessType("order")
                .businessId("ORDER_FAILED")
                .remark("测试标记失败")
                .build();

        SysLocalMessage savedMessage = sysLocalMessageService.saveLocalMessage(request);

        // 标记为失败
        boolean result = sysLocalMessageService.markMessageFailed(
                savedMessage.getMessageId(), 
                savedMessage.getVersion(),
                "测试失败原因"
        );

        // 验证结果
        assertTrue(result);
        System.out.println("标记消息失败结果: " + result);
    }

    @Test
    public void testDuplicateMessage() {
        // 准备测试数据
        SaveLocalMessageRequest request = SaveLocalMessageRequest.builder()
                .messageId("TEST_MSG_DUPLICATE")
                .exchange("order-exchange")
                .routingKey("order.created")
                .messageBody("{\"orderId\":\"ORDER_DUPLICATE\",\"userId\":\"USER_DUPLICATE\"}")
                .businessType("order")
                .businessId("ORDER_DUPLICATE")
                .remark("测试重复消息")
                .build();

        // 第一次保存
        SysLocalMessage firstMessage = sysLocalMessageService.saveLocalMessage(request);
        assertNotNull(firstMessage);

        // 第二次保存相同的消息ID
        SysLocalMessage secondMessage = sysLocalMessageService.saveLocalMessage(request);
        assertNotNull(secondMessage);

        // 验证返回的是同一条消息
        assertEquals(firstMessage.getId(), secondMessage.getId());
        System.out.println("重复消息处理正确");
    }
}
