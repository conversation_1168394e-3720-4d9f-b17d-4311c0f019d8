# 本地消息表设计方案

## 概述

本地消息表是一种分布式事务解决方案，用于处理MQ消息发送失败的情况。当消息发送到MQ失败时，将消息保存到本地数据库中，通过定时任务进行重试，确保消息最终能够成功发送。

## 设计特点

### 1. 可靠性保证
- 消息发送失败时自动保存到本地数据库
- 支持最多3次重试，采用指数退避策略（1分钟、5分钟、15分钟）
- 超过重试次数后标记为最终失败

### 2. 高性能
- 使用乐观锁避免并发问题
- 索引优化，支持高效查询
- 批量处理提高效率

### 3. 易用性
- 提供统一的API接口
- 支持多种业务类型（订单、用户、支付等）
- 集成现有的消息生产者

## 核心组件

### 1. 数据库表结构

```sql
CREATE TABLE `sys_local_message` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `message_id` varchar(64) NOT NULL COMMENT '消息ID，全局唯一',
  `exchange` varchar(100) NOT NULL COMMENT '交换机名称',
  `routing_key` varchar(100) NOT NULL COMMENT '路由键',
  `message_body` text NOT NULL COMMENT '消息体内容',
  `message_headers` text COMMENT '消息头信息，JSON格式',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '消息状态',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `max_retry_count` int(11) NOT NULL DEFAULT '3' COMMENT '最大重试次数',
  `next_retry_time` datetime DEFAULT NULL COMMENT '下次重试时间',
  `last_retry_time` datetime DEFAULT NULL COMMENT '最后重试时间',
  `failure_reason` varchar(500) DEFAULT NULL COMMENT '失败原因',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `business_id` varchar(64) DEFAULT NULL COMMENT '业务ID',
  `sent_time` datetime DEFAULT NULL COMMENT '发送成功时间',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号，用于乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_message_id` (`message_id`),
  KEY `idx_status_retry_time` (`status`, `next_retry_time`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_business_id` (`business_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='本地消息表';
```

### 2. 消息状态枚举

- `PENDING`: 待发送
- `SENDING`: 发送中
- `SUCCESS`: 发送成功
- `FAILED`: 发送失败（超过最大重试次数）

### 3. 定时任务

使用XXL-Job实现定时重试：

- **localMessageRetryJob**: 每5分钟执行一次，处理失败的消息重试
- **cleanSuccessMessagesJob**: 每天凌晨2点执行，清理过期的成功消息

## 使用方式

### 1. 在其他服务中使用

#### 方式一：使用LocalMessageHelper工具类

```java
@Resource
private LocalMessageHelper localMessageHelper;

// 保存订单消息
localMessageHelper.saveOrderMessage("order.created", messageBody, orderId);

// 保存用户消息
localMessageHelper.saveUserMessage("user.operation.record", messageBody, userId);

// 保存支付消息
localMessageHelper.savePaymentMessage("payment.result", messageBody, paymentId);

// 保存并立即发送消息
boolean success = localMessageHelper.saveAndSendMessage(
    "order-exchange", 
    "order.created", 
    messageBody, 
    "order", 
    orderId
);
```

#### 方式二：使用Feign客户端

```java
@Resource
private LocalMessageFeignClient localMessageFeignClient;

SaveLocalMessageRequest request = SaveLocalMessageRequest.builder()
    .messageId("UNIQUE_MESSAGE_ID")
    .exchange("order-exchange")
    .routingKey("order.created")
    .messageBody(messageBody)
    .businessType("order")
    .businessId(orderId)
    .build();

// 保存消息
localMessageFeignClient.saveLocalMessage(request);

// 保存并发送消息
HttpResult<Boolean> result = localMessageFeignClient.saveAndSendMessage(request);
```

### 2. 集成现有的消息生产者

以OrderProducer为例：

```java
@Resource
private LocalMessageHelper localMessageHelper;

public void sendOrderCreateEvent(String messageBody, String orderId) {
    // 原有的发送逻辑
    rabbitTemplate.convertAndSend("order-exchange", "order.created", message, correlationData);
    
    // 添加失败回调
    correlationData.getFuture().addCallback(
        result -> {
            if (!result.isAck()) {
                // 消息未到达Broker，保存到本地消息表
                localMessageHelper.saveOrderMessage("order.created", messageBody, orderId);
            }
        },
        ex -> {
            // 消息发送异常，保存到本地消息表
            localMessageHelper.saveOrderMessage("order.created", messageBody, orderId);
        }
    );
}
```

### 3. 管理接口

提供了完整的管理接口：

- `POST /userServices/localMessage/save` - 保存本地消息
- `POST /userServices/localMessage/saveAndSend` - 保存并发送消息
- `POST /userServices/localMessage/resend/{messageId}` - 重新发送指定消息
- `POST /userServices/localMessage/batchResend` - 批量重新发送消息
- `POST /userServices/localMessage/processRetry` - 手动处理重试消息
- `GET /userServices/localMessage/{messageId}` - 查询消息详情
- `GET /userServices/localMessage/page` - 分页查询消息列表
- `GET /userServices/localMessage/retry` - 获取需要重试的消息

## 配置说明

### 1. XXL-Job配置

在user-services的配置文件中添加：

```yaml
xxl:
  job:
    admin:
      addresses: http://***********:7777/xxl-job-admin
    accessToken: default_token
    executor:
      appname: user-services
      address: 
      ip: 
      port: 9999
      logpath: ./logs/xxl-job
      logretentiondays: 30
```

### 2. 任务配置

在XXL-Job管理后台配置定时任务：

1. **本地消息重试任务**
   - 任务名称：localMessageRetryJob
   - Cron表达式：`0 */5 * * * ?` （每5分钟执行一次）
   - 任务参数：`{"limit": 100}` （可选，指定每次处理的消息数量）

2. **清理成功消息任务**
   - 任务名称：cleanSuccessMessagesJob
   - Cron表达式：`0 0 2 * * ?` （每天凌晨2点执行）
   - 任务参数：`7` （可选，指定清理天数）

## 监控和运维

### 1. 日志监控

- 消息保存日志：记录消息保存成功/失败
- 重试日志：记录重试过程和结果
- 异常日志：记录各种异常情况

### 2. 指标监控

建议监控以下指标：

- 待重试消息数量
- 重试成功率
- 最终失败消息数量
- 平均重试次数

### 3. 告警机制

建议设置以下告警：

- 待重试消息数量过多
- 重试成功率过低
- 最终失败消息数量异常增长

## 最佳实践

### 1. 消息ID生成

确保消息ID全局唯一，建议格式：`KNET_B2B_{BUSINESS_TYPE}_MSG_{RANDOM_STRING}`

### 2. 业务类型规范

建议使用统一的业务类型命名：

- `order` - 订单相关
- `user` - 用户相关
- `payment` - 支付相关
- `goods` - 商品相关

### 3. 重试策略

当前采用指数退避策略：1分钟、5分钟、15分钟，可根据业务需要调整。

### 4. 数据清理

定期清理成功发送的消息，避免数据表过大影响性能。

## 扩展性

### 1. 支持更多业务类型

通过扩展LocalMessageHelper工具类，可以轻松支持新的业务类型。

### 2. 自定义重试策略

可以通过修改重试间隔数组来调整重试策略。

### 3. 集成其他MQ

当前实现基于RabbitMQ，可以扩展支持其他消息队列。

## 注意事项

1. 确保数据库连接池配置合理，避免连接耗尽
2. 定时任务的执行频率要合理，避免对数据库造成压力
3. 消息体大小要控制在合理范围内
4. 定期监控和清理数据，避免表数据过大
5. 在高并发场景下，注意乐观锁的重试机制
